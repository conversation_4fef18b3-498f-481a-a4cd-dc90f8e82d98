﻿using Struxit2._0.Model;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Struxit2._0.Interfaces;

namespace Struxit2._0.DataAccess
{
    public class TransmittalRespository : ITransmittalRespository
    {
        private readonly string connString = ConfigurationManager.ConnectionStrings["Production"].ConnectionString;
        private readonly ILogger<TransmittalRespository> _logger;

        public TransmittalRespository()
        {
            var loggerFactory = new LoggerFactory();
            var logger = loggerFactory.CreateLogger<TransmittalRespository>();

            _logger = logger;
        }

        public void Clear(TransmittalRespositoryModel transmittal)
        {
            transmittal.Id = 0;
            transmittal.TransNo = string.Empty;
            transmittal.No = string.Empty;
            transmittal.IsDeleted = 0;
            transmittal.DateDeleted = string.Empty;
            transmittal.Date = string.Empty;
            transmittal.CompanyID = string.Empty;
            transmittal.PersonID = string.Empty;
            transmittal.ProjectName = string.Empty;
            transmittal.ProjectID = string.Empty;
        }

        public bool Load(int contentID)
        {
            var transmittal = new TransmittalRespositoryModel();
            _logger.LogInformation("Starting Load query for Transmittal ID: {contentID}", contentID);

            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");

                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [ID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", contentID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                transmittal.Id = (int)reader["id"];
                                transmittal.TransNo = (string)reader["TransNo"];
                                transmittal.No = (string)reader["No"];
                                transmittal.IsDeleted = (int)reader["isDeleted"];
                                transmittal.DateDeleted = reader["DateDeleted"].ToString();
                                transmittal.Date = reader["Date"].ToString();
                                transmittal.CompanyID = (string)reader["CompanyID"];
                                transmittal.PersonID = (string)reader["PersonID"];
                                transmittal.ProjectName = (string)reader["ProjectName"];
                                transmittal.ProjectID = (string)reader["ProjectID"];

                                _logger.LogInformation("Query for Transmittal ID: {contentID} Returned true", contentID);
                                return true;
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                        contentID, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        contentID, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
                return false;
            }
        }

        public bool UpdateTransNoAndDate(int id, string newTransNo, string newDate)
        {
            newTransNo = newTransNo ?? "";

            _logger.LogInformation("Starting UpdateTransNoAndDate query for Transmittal ID: {id}", id);

            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE [dbo].[Transmittals]
                        SET [TransNo] = @TransNo, [Date] = @Date
                        WHERE [ID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.Parameters.AddWithValue("@TransNo", newTransNo);
                        cmd.Parameters.AddWithValue("@Date", newDate);
                        int rowsAffected = cmd.ExecuteNonQuery();
                        _logger.LogInformation("Query for UpdateTransNoAndDate the update has affected {rowsAffected} row", rowsAffected);
                        return rowsAffected > 0;
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                        id, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        id, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
                return false;
            }
        }

        public TransmittalModel LoadData(int contentID)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [ID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", contentID);
                        TransmittalModel transmittal = new TransmittalModel();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                transmittal.Id = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0;
                                transmittal.TransNo = reader["TransNo"] != DBNull.Value ? (string)reader["TransNo"] : null;
                                transmittal.No = reader["No"] != DBNull.Value ? (string)reader["No"] : null;
                                transmittal.IsDeleted = reader["isDeleted"] != DBNull.Value ? ((bool)reader["isDeleted"] ? 1 : 0) : 0;
                                transmittal.DateDeleted = reader["DateDeleted"] != DBNull.Value ? reader["DateDeleted"].ToString() : null;
                                transmittal.Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null;
                                transmittal.CompanyID = reader["CompanyID"] != DBNull.Value ? (string)reader["CompanyID"] : null;
                                transmittal.PersonID = reader["PersonID"] != DBNull.Value ? (string)reader["PersonID"] : null;
                                transmittal.ProjectName = reader["ProjectName"] != DBNull.Value ? (string)reader["ProjectName"] : null;
                                transmittal.ProjectID = reader["ProjectID"] != DBNull.Value ? (string)reader["ProjectID"] : null;
                                _logger.LogInformation("Query for Load Data returned transmittal ID", transmittal.Id);
                                return transmittal;
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                        contentID, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        contentID, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
                return null;
            }
        }

        public bool UpdateTransmittalToDeleted(int id)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    string sql = "UPDATE [dbo].[Transmittals] SET [isDeleted] = 1, [DateDeleted] = @DateDeleted WHERE [ID] = @ID";

                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.Parameters.AddWithValue("@DateDeleted", DateTime.Now);

                        int rowsAffected = cmd.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                        id, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        id, ex.Message);
                    return false;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");


                }
            }
        }

        public int Save(TransmittalRespositoryModel transmittal)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    string sql;
                    if (transmittal.Id == 0)
                    {
                        sql = "INSERT INTO [dbo].[Transmittals] ([TransNo], [No], [isDeleted], [DateDeleted], [Date], [CompanyID], [PersonID], [ProjectName], [ProjectID]) " +
                              "VALUES (@TransNo, @No, @isDeleted, @DateDeleted, @Date, @CompanyID, @PersonID, @ProjectName, @ProjectID); " +
                              "SELECT SCOPE_IDENTITY();";
                    }
                    else
                    {
                        sql = "UPDATE [dbo].[Transmittals] SET [TransNo] = @TransNo, [No] = @No, [isDeleted] = @isDeleted, [DateDeleted] = @DateDeleted, " +
                              "[Date] = @Date, [CompanyID] = @CompanyID, [PersonID] = @PersonID, [ProjectName] = @ProjectName, [ProjectID] = @ProjectID " +
                              "WHERE [ID] = @ID;";
                    }

                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@TransNo", transmittal.TransNo);
                        cmd.Parameters.AddWithValue("@No", transmittal.No);
                        cmd.Parameters.AddWithValue("@isDeleted", transmittal.IsDeleted);
                        cmd.Parameters.AddWithValue("@DateDeleted", transmittal.DateDeleted);
                        cmd.Parameters.AddWithValue("@Date", transmittal.Date);
                        cmd.Parameters.AddWithValue("@CompanyID", transmittal.CompanyID);
                        cmd.Parameters.AddWithValue("@PersonID", transmittal.PersonID);
                        cmd.Parameters.AddWithValue("@ProjectName", transmittal.ProjectName);
                        cmd.Parameters.AddWithValue("@ProjectID", transmittal.ProjectID);

                        if (transmittal.Id != 0)
                        {
                            cmd.Parameters.AddWithValue("@ID", transmittal.Id);
                            cmd.ExecuteNonQuery();
                            _logger.LogInformation("Query for Save returned id:", transmittal.Id);
                            return transmittal.Id;
                        }
                        else
                        {
                            var id = Convert.ToInt32(cmd.ExecuteScalar());
                            _logger.LogInformation("Query for Save returned id:", id);

                            return id;
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                        "", sqlEx.Message, sqlEx.Number);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        "", ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
                return 0;
            }
        }

        public int CreateNewTransmittal(string _TransNo, string No, string IsDeleted,
            string DateDeleted, string Date, string CompanyID, string PersonID, string ProjectName, string ProjectID)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    string sql;

                    sql = "INSERT INTO [dbo].[Transmittals] ([TransNo], [No], [isDeleted], [DateDeleted], [Date], [CompanyID], [PersonID], [ProjectName], [ProjectID]) " +
                          "VALUES (@TransNo, @No, @isDeleted, @DateDeleted, @Date, @CompanyID, @PersonID, @ProjectName, @ProjectID); " +
                          "SELECT SCOPE_IDENTITY();";

                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@TransNo", _TransNo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@No", No ?? (object)DBNull.Value);

                        // Handle BIT field properly
                        cmd.Parameters.AddWithValue("@isDeleted", IsDeleted == "1" || IsDeleted?.ToLower() == "true");

                        // Handle DATETIME fields properly
                        if (string.IsNullOrEmpty(DateDeleted))
                            cmd.Parameters.AddWithValue("@DateDeleted", DBNull.Value);
                        else if (DateTime.TryParse(DateDeleted, out DateTime parsedDateDeleted))
                            cmd.Parameters.AddWithValue("@DateDeleted", parsedDateDeleted);
                        else
                            cmd.Parameters.AddWithValue("@DateDeleted", DBNull.Value);

                        if (DateTime.TryParse(Date, out DateTime parsedDate))
                            cmd.Parameters.AddWithValue("@Date", parsedDate);
                        else
                            cmd.Parameters.AddWithValue("@Date", DateTime.Now);

                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PersonID", PersonID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProjectName", ProjectName ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProjectID", ProjectID ?? (object)DBNull.Value);


                        var result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            var id = Convert.ToInt32(result);
                            _logger.LogInformation("Query for Save returned id: {id}", id);
                            return id;
                        }
                        else
                        {
                            _logger.LogWarning("ExecuteScalar returned null or DBNull");
                            return 0;
                        }
                    }

                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       _TransNo, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        _TransNo, ex.Message);
                    return 0;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
        }

        public List<TransmittalRespositoryModel.Item> ListAllTransmittalsFromProject(string projectName)
        {
            List<TransmittalRespositoryModel.Item> result = new List<TransmittalRespositoryModel.Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [ProjectName] LIKE @ProjectName;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ProjectName", $"%{projectName}%");
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                TransmittalRespositoryModel.Item item = new TransmittalRespositoryModel.Item
                                {
                                    Id = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                                    TransNo = reader["TransNo"] != DBNull.Value ? (string)reader["TransNo"] : null,
                                    No = reader["No"] != DBNull.Value ? (string)reader["No"] : null,
                                    IsDeleted = reader["isDeleted"] != DBNull.Value ? ((bool)reader["isDeleted"] ? 1 : 0) : 0,
                                    DateDeleted = reader["DateDeleted"] != DBNull.Value ? reader["DateDeleted"].ToString() : null,
                                    Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null,
                                    CompanyID = reader["CompanyID"] != DBNull.Value ? (string)reader["CompanyID"] : null,
                                    PersonID = reader["PersonID"] != DBNull.Value ? (string)reader["PersonID"] : null,
                                    ProjectName = reader["ProjectName"] != DBNull.Value ? (string)reader["ProjectName"] : null,
                                    ProjectID = reader["ProjectID"] != DBNull.Value ? (string)reader["ProjectID"] : null
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       projectName, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        projectName, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }

            }
            return result;
        }

        public async Task<List<TransmittalRespositoryModel>> ListAllByNameAsync(string ProjectName)
        {
            List<TransmittalRespositoryModel> result = new List<TransmittalRespositoryModel>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    await sqlConn.OpenAsync();
                    _logger.LogDebug("SQL connection opened successfully");
                    string query = @"SELECT * FROM [dbo].[Transmittals] WHERE [ProjectName] LIKE @ProjectName;";
                    using (SqlCommand cmd = new SqlCommand(query, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ProjectName", ProjectName);
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {


                                TransmittalRespositoryModel transmittal = new TransmittalRespositoryModel
                                {
                                    Id = (int)reader["id"],
                                    TransNo = (string)reader["TransNo"],
                                    No = (string)reader["No"],
                                    IsDeleted = (int)reader["isDeleted"],
                                    DateDeleted = reader["DateDeleted"].ToString(),
                                    Date = reader["Date"].ToString(),
                                    CompanyID = (string)reader["CompanyID"],
                                    PersonID = (string)reader["PersonID"],
                                    ProjectName = (string)reader["ProjectName"],
                                    ProjectID = (string)reader["ProjectID"]
                                };
                                result.Add(transmittal);
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       ProjectName, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        ProjectName, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
            return result;
        }

        public async Task<List<TransmittalRespositoryModel>> ListAllByNameWhereZeroAsync(string projectName)
        {
            List<TransmittalRespositoryModel> result = new List<TransmittalRespositoryModel>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    await sqlConn.OpenAsync();
                    _logger.LogDebug("SQL connection opened successfully");
                    string query = @"SELECT * FROM [dbo].[Transmittals] 
                             WHERE [ProjectName] LIKE @ProjectName AND [isDeleted] = 0;";
                    using (SqlCommand cmd = new SqlCommand(query, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ProjectName", "%" + projectName + "%");
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                TransmittalRespositoryModel transmittal = new TransmittalRespositoryModel
                                {
                                    Id = (int)reader["id"],
                                    TransNo = (string)reader["TransNo"],
                                    No = (string)reader["No"],
                                    IsDeleted = (int)reader["isDeleted"],
                                    DateDeleted = reader["DateDeleted"] != DBNull.Value ? reader["DateDeleted"].ToString() : null,
                                    Date = reader["Date"].ToString(),
                                    CompanyID = (string)reader["CompanyID"],
                                    PersonID = (string)reader["PersonID"],
                                    ProjectName = (string)reader["ProjectName"],
                                    ProjectID = (string)reader["ProjectID"]
                                };
                                result.Add(transmittal);
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       projectName, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        projectName, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
            return result;
        }

        public List<TransmittalRespositoryModel.Item> ListAllTransmittalsFromProjectAndCompany(string projectName, string company)
        {
            List<TransmittalRespositoryModel.Item> result = new List<TransmittalRespositoryModel.Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [ProjectName] LIKE @ProjectName AND [CompanyID] = @CompanyID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ProjectName", $"%{projectName}%");
                        cmd.Parameters.AddWithValue("@CompanyID", company);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                TransmittalRespositoryModel.Item item = new TransmittalRespositoryModel.Item
                                {
                                    Id = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                                    TransNo = reader["TransNo"] != DBNull.Value ? (string)reader["TransNo"] : null,
                                    No = reader["No"] != DBNull.Value ? (string)reader["No"] : null,
                                    IsDeleted = reader["isDeleted"] != DBNull.Value ? ((bool)reader["isDeleted"] ? 1 : 0) : 0,
                                    DateDeleted = reader["DateDeleted"] != DBNull.Value ? reader["DateDeleted"].ToString() : null,
                                    Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null,
                                    CompanyID = reader["CompanyID"] != DBNull.Value ? (string)reader["CompanyID"] : null,
                                    PersonID = reader["PersonID"] != DBNull.Value ? (string)reader["PersonID"] : null,
                                    ProjectName = reader["ProjectName"] != DBNull.Value ? (string)reader["ProjectName"] : null,
                                    ProjectID = reader["ProjectID"] != DBNull.Value ? (string)reader["ProjectID"] : null
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       projectName, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        projectName, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
            return result;
        }

        public List<TransmittalRespositoryModel.Item> ListAllTransmittalsFromProjectAndCompanyWhereIsDeleted(string projectName, string company)
        {
            List<TransmittalRespositoryModel.Item> result = new List<TransmittalRespositoryModel.Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [ProjectName] LIKE @ProjectName AND [CompanyID] = @CompanyID AND [isDeleted] = 0;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ProjectName", $"%{projectName}%");
                        cmd.Parameters.AddWithValue("@CompanyID", company);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                TransmittalRespositoryModel.Item item = new TransmittalRespositoryModel.Item
                                {
                                    Id = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                                    TransNo = reader["TransNo"] != DBNull.Value ? (string)reader["TransNo"] : null,
                                    No = reader["No"] != DBNull.Value ? (string)reader["No"] : null,
                                    IsDeleted = reader["isDeleted"] != DBNull.Value ? ((bool)reader["isDeleted"] ? 1 : 0) : 0,
                                    DateDeleted = reader["DateDeleted"] != DBNull.Value ? reader["DateDeleted"].ToString() : null,
                                    Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null,
                                    CompanyID = reader["CompanyID"] != DBNull.Value ? (string)reader["CompanyID"] : null,
                                    PersonID = reader["PersonID"] != DBNull.Value ? (string)reader["PersonID"] : null,
                                    ProjectName = reader["ProjectName"] != DBNull.Value ? (string)reader["ProjectName"] : null,
                                    ProjectID = reader["ProjectID"] != DBNull.Value ? (string)reader["ProjectID"] : null
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       projectName, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        projectName, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
            return result;
        }

        public List<TransmittalRespositoryModel.Item> LoadAllDeletedTransmittals()
        {
            List<TransmittalRespositoryModel.Item> result = new List<TransmittalRespositoryModel.Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [isDeleted] = 1;", sqlConn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                TransmittalRespositoryModel.Item item = new TransmittalRespositoryModel.Item
                                {
                                    Id = (int)reader["id"],
                                    TransNo = (string)reader["TransNo"],
                                    No = (string)reader["No"],
                                    IsDeleted = (int)reader["isDeleted"],
                                    DateDeleted = reader["DateDeleted"].ToString(),
                                    Date = reader["Date"].ToString(),
                                    CompanyID = (string)reader["CompanyID"],
                                    PersonID = (string)reader["PersonID"],
                                    ProjectName = (string)reader["ProjectName"],
                                    ProjectID = (string)reader["ProjectID"]
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {TransNo}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       "TransNo", sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {TransNo}. Error: {ErrorMessage}",
                        "TransNo", ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
            return result;
        }


        public bool LoadAllWhereCompanyID(int id)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(1) FROM [dbo].[Transmittals] WHERE [CompanyID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", id);

                        object result = cmd.ExecuteScalar();

                        return result != null && Convert.ToInt32(result) > 0;
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {TransNo}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       "TransNo", sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {TransNo}. Error: {ErrorMessage}",
                        "TransNo", ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
        }




        public bool RestoreTransmittalData(int id)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    _logger.LogDebug("SQL connection opened successfully");
                    string sql = "UPDATE [dbo].[Transmittals] SET [isDeleted] = 0, [DateDeleted] = @DateDeleted WHERE [ID] = @ID";

                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.Parameters.AddWithValue("@DateDeleted", DateTime.Now);

                        int rowsAffected = cmd.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
                catch (SqlException sqlEx)
                {
                    _logger.LogError(sqlEx, "SQL error occurred while retrieving transmittals for transmittal id {contentID}. Error: {ErrorMessage}, Code: {ErrorCode}",
                       id, sqlEx.Message, sqlEx.Number);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred while retrieving transmittals for for transmittal id {contentID}. Error: {ErrorMessage}",
                        id, ex.Message);
                    throw;
                }
                finally
                {
                    sqlConn.Close();
                    _logger.LogDebug("SQL connection closed successfully");
                }
            }
        }
    }
}

