@{
    ViewBag.Title = "Debug PDF Text Extraction";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4><i class="bi bi-bug"></i> Debug PDF Text Extraction</h4>
                    <p class="mb-0">Upload a PDF to see exactly what text is being extracted</p>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(ViewBag.Message))
                    {
                        <div class="alert alert-warning">
                            @ViewBag.Message
                        </div>
                    }

                    <form method="post" enctype="multipart/form-data" action="@Url.Action("DebugPdfExtraction")">
                        <div class="mb-3">
                            <label for="file" class="form-label">Select PDF File</label>
                            <input type="file" class="form-control" id="file" name="file" accept=".pdf" required>
                            <div class="form-text">Choose a PDF file to analyze its text content</div>
                        </div>
                        
                        <button type="submit" class="btn btn-info">
                            <i class="bi bi-search"></i> Extract & Debug Text
                        </button>
                    </form>

                    <div class="mt-4">
                        <h6>What this will show you:</h6>
                        <ul>
                            <li><strong>Raw Text:</strong> Exactly what text the PDF extraction library sees</li>
                            <li><strong>Text Layout:</strong> How the text is structured (line breaks, spacing, etc.)</li>
                            <li><strong>Pattern Matching:</strong> Which regex patterns (if any) match your PDF format</li>
                            <li><strong>Missing Data:</strong> Why description, revision, or sheet size might show as "not found"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
