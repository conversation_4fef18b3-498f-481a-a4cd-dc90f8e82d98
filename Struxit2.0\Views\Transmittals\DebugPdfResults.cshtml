@model List<Struxit2._0.Models.DrawingInfo>
@{
    ViewBag.Title = "PDF Text Extraction Debug Results";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4><i class="bi bi-file-earmark-text"></i> Debug Results for: @ViewBag.FileName</h4>
                </div>
                <div class="card-body">
                    @if (Model != null && Model.Any())
                    {
                        @foreach (var item in Model)
                        {
                            @if (item.DocNo.StartsWith("DEBUG_PAGE_"))
                            {
                                <div class="alert alert-info">
                                    <h5><i class="bi bi-info-circle"></i> @item.DocNo</h5>
                                    <h6>Raw Extracted Text:</h6>
                                    <div class="bg-light p-3 border rounded">
                                        <pre style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 12px;">@item.Description.Replace("RAW_EXTRACTED_TEXT: ", "")</pre>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-success">
                                    <h5><i class="bi bi-check-circle"></i> Pattern Match Found!</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>Doc No:</strong><br>
                                            <code>@item.DocNo</code>
                                        </div>
                                        <div class="col-md-2">
                                            <strong>Revision:</strong><br>
                                            <code>@item.Rev</code>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Description:</strong><br>
                                            <code>@item.Description</code>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Sheet Size:</strong><br>
                                            <code>@(item.Size ?? "Not detected")</code>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <h5><i class="bi bi-exclamation-triangle"></i> No Data Extracted</h5>
                            <p>The PDF text extraction didn't return any results. This could mean:</p>
                            <ul>
                                <li>The PDF is image-based (scanned) and contains no extractable text</li>
                                <li>The PDF has security restrictions preventing text extraction</li>
                                <li>There was an error processing the PDF file</li>
                            </ul>
                        </div>
                    }

                    <div class="mt-4">
                        <h6>Understanding the Results:</h6>
                        <ul>
                            <li><strong>DEBUG_PAGE_X entries:</strong> Show the raw text extracted from each page</li>
                            <li><strong>Pattern Match entries:</strong> Show successful regex pattern matches</li>
                            <li><strong>Pipe symbols (|):</strong> Represent line breaks in the original text</li>
                            <li><strong>No pattern matches:</strong> Means your PDF format doesn't match any existing regex patterns</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <a href="@Url.Action("DebugPdfExtraction")" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Test Another PDF
                        </a>
                        <a href="@Url.Action("Index")" class="btn btn-primary">
                            <i class="bi bi-house"></i> Back to Transmittals
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
