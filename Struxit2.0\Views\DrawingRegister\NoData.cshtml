@{
    ViewBag.Title = "No Drawing Register Data";
    Layout = "~/Views/Shared/_layout1.cshtml";
}

<div class="pagetitle">
    <h1>Drawing Register - No Data Found</h1>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">No Transmittal Data Available</h5>
                    
                    <div class="alert alert-warning" role="alert">
                        <h4 class="alert-heading">Missing Data!</h4>
                        <p><strong>Project:</strong> @ViewBag.ProjectName</p>
                        <p>@ViewBag.ErrorMessage</p>
                        <hr>
                        <p class="mb-0">
                            To resolve this issue:
                            <ol>
                                <li>Ensure that transmittals have been created for this project</li>
                                <li>Check that the project name matches exactly</li>
                                <li>Verify that the database connection is working</li>
                                <li>Make sure transmittals are not marked as deleted</li>
                            </ol>
                        </p>
                    </div>

                    <div class="mt-3">
                        <a href="@Url.Action("Index", "Hubs")" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Back to Projects
                        </a>

                        <a href="@Url.Action("SelectPage", "Hubs", new { projectName = ViewBag.ProjectName })" class="btn btn-secondary">
                            <i class="bi bi-arrow-clockwise"></i> Try Again
                        </a>

                        <a href="@Url.Action("CreateTestData", new { projectName = ViewBag.ProjectName })" class="btn btn-warning">
                            <i class="bi bi-database-add"></i> Create Test Data
                        </a>
                    </div>

                    <div class="mt-4">
                        <h6>Debug Information:</h6>
                        <ul>
                            <li><strong>Project Name:</strong> @ViewBag.ProjectName</li>
                            <li><strong>Time:</strong> @DateTime.Now</li>
                            <li><strong>Action:</strong> SelectCompany</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
