@{
    ViewBag.Title = "Create Test Data";
    Layout = "~/Views/Shared/_layout1.cshtml";
}

<div class="pagetitle">
    <h1>Create Test Data for Drawing Register</h1>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Test Data Creation</h5>
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Message))
                    {
                        <div class="alert @(ViewBag.Success == true ? "alert-success" : "alert-danger")" role="alert">
                            @ViewBag.Message
                        </div>
                    }

                    @if (ViewBag.Success == true)
                    {
                        <div class="mt-3">
                            <a href="@Url.Action("SelectCompany", new { projectName = ViewBag.ProjectName })" class="btn btn-primary">
                                <i class="bi bi-arrow-right"></i> Go to Drawing Register
                            </a>

                            <a href="@Url.Action("ClearTestData", new { projectName = ViewBag.ProjectName })" class="btn btn-warning ms-2"
                               onclick="return confirm('Are you sure you want to clear all test data for this project?')">
                                <i class="bi bi-trash"></i> Clear Test Data
                            </a>
                        </div>
                    }
                    else
                    {
                        <form method="get" action="@Url.Action("CreateTestData")">
                            <div class="mb-3">
                                <label for="projectName" class="form-label">Project Name</label>
                                <input type="text" class="form-control" id="projectName" name="projectName" 
                                       value="@ViewBag.ProjectName" placeholder="Enter project name" required>
                                <div class="form-text">Enter the exact project name for which you want to create test data.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Create Comprehensive Test Data</button>
                        </form>

                        @if (!string.IsNullOrEmpty(ViewBag.ProjectName))
                        {
                            <div class="mt-2">
                                <a href="@Url.Action("ClearTestData", new { projectName = ViewBag.ProjectName })" class="btn btn-outline-warning btn-sm"
                                   onclick="return confirm('Are you sure you want to clear all test data for this project?')">
                                    <i class="bi bi-trash"></i> Clear Existing Test Data First
                                </a>
                            </div>
                        }
                    }

                    <div class="mt-4">
                        <h6>What this creates:</h6>
                        <ul>
                            <li><strong>Companies:</strong> Century Property, Ayala Land Inc., SM Development Corporation, Megaworld Corporation, and more</li>
                            <li><strong>Persons:</strong> Maria Santos, Juan Dela Cruz, Anna Reyes, Carlos Mendoza, and more</li>
                            <li><strong>Transmittals:</strong> 5 different transmittals (TR-001 to TR-005) with different dates and companies</li>
                            <li><strong>Drawing Sheets:</strong> 14 architectural, structural, electrical, and mechanical drawings</li>
                            <li><strong>Revisions:</strong> Multiple revisions linking sheets to transmittals with different revision numbers and dates</li>
                            <li><strong>Complete Test Environment:</strong> Enables full Drawing Register functionality for comprehensive testing</li>
                        </ul>

                        <div class="alert alert-info mt-3">
                            <strong>Note:</strong> This creates a realistic dataset similar to actual construction projects,
                            including multiple companies, various drawing types (A-series, S-series, E-series, M-series),
                            and revision histories that you can use to thoroughly test all Drawing Register features.
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="@Url.Action("Index", "Hubs")" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Projects
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
