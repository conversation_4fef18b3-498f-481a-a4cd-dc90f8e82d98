@{
    ViewBag.Title = "Create Test Data";
    Layout = "~/Views/Shared/_layout1.cshtml";
}

<div class="pagetitle">
    <h1>Create Test Data for Drawing Register</h1>
</div>

<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Test Data Creation</h5>
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Message))
                    {
                        <div class="alert @(ViewBag.Success == true ? "alert-success" : "alert-danger")" role="alert">
                            @ViewBag.Message
                        </div>
                    }

                    @if (ViewBag.Success == true)
                    {
                        <div class="mt-3">
                            <a href="@Url.Action("SelectCompany", new { projectName = ViewBag.ProjectName })" class="btn btn-primary">
                                <i class="bi bi-arrow-right"></i> Go to Drawing Register
                            </a>
                        </div>
                    }
                    else
                    {
                        <form method="get" action="@Url.Action("CreateTestData")">
                            <div class="mb-3">
                                <label for="projectName" class="form-label">Project Name</label>
                                <input type="text" class="form-control" id="projectName" name="projectName" 
                                       value="@ViewBag.ProjectName" placeholder="Enter project name" required>
                                <div class="form-text">Enter the exact project name for which you want to create test data.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Create Test Data</button>
                        </form>
                    }

                    <div class="mt-4">
                        <h6>What this does:</h6>
                        <ul>
                            <li>Creates a test transmittal record in the database</li>
                            <li>Associates it with the specified project name</li>
                            <li>Uses default company and person IDs</li>
                            <li>Enables the Drawing Register functionality for testing</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <a href="@Url.Action("Index", "Hubs")" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Projects
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
