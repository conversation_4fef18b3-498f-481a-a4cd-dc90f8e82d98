﻿using Struxit2._0.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using Struxit2._0.DataAccess;
using MongoDB.Driver;
using System.Text.RegularExpressions;
using System.IO;
using UglyToad.PdfPig;
using UglyToad.PdfPig.Content;
using Struxit2._0.Helpers;
using Struxit2._0.Services;
using Newtonsoft.Json;
using System.Web.UI.WebControls;
using ClosedXML.Excel;
using Struxit2._0.Interfaces;

namespace Struxit2._0.Controllers
{
    public class TransmittalsController : Controller
    {
        private readonly MongoDBManager _mongoManager;

        private readonly PdfService _pdfService;

        private readonly TransmittalService _transmittalService;

        public TransmittalsController(TransmittalService transmittalService)
        {
            _mongoManager = new MongoDBManager();
            _pdfService = new PdfService();
            _transmittalService = transmittalService;
        }

        public async Task<ActionResult> ManageTransmittals()
        {
            DataAccess.Project project = new DataAccess.Project();
            var model = project.ListAll();

            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> ManageTransmittals(string searchTerm)
        {
            var projectNames = await LoadDataAsync();
            var filteredProjectNames = projectNames.Where(p => p.Project_Name.ToLower().Contains(searchTerm.ToLower())).ToList();

            var model = new ManageTransmittalsViewModel
            {
                SearchTerm = searchTerm,
                ProjectNames = filteredProjectNames
            };

            return View(model);
        }

        private async Task<List<ProjectModel>> LoadDataAsync()
        {
            var list = await _mongoManager.ViewAllDataOfObjectId(_mongoManager.ConnectToDatabase());
            var uniqueProjects = list.GroupBy(p => p.Project_Name).Select(g => g.First()).ToList();
            return uniqueProjects;
        }

        public async Task<ActionResult> ManageViewAllTransmittal(string name)
        {
            try
            {
                var transmittals = await LoadSQLTransmittalsAsyncWhereIsDeletedEqualsZero(name);

                var model = new TransmittalViewModel
                {
                    ProjectName = name,
                    Transmittals = transmittals
                };

                return View(model);
            }
            catch (Exception ex)
            {

            }

            var modeler = new TransmittalViewModel();
            return View(modeler);
        }

        [HttpPost]
        public async Task<ActionResult> EditTransmittal(TransmittalViewModel model)
        {
            try
            {

                if (!int.TryParse(model.Id, out int transmittalId))
                {
                    //return BadRequest("Invalid Transmittal ID.");
                }

                var revEntryTransmittal = new DocRevTransmittal();
                var revEntries = await revEntryTransmittal.LoadAllByTransidAsync(transmittalId);
                if (revEntries == null)
                {
                    //return NotFound("No RevEntries found for the specified Transmittal ID.");
                }

                // Load Transmittal

                var loadedTransmittal = _transmittalService.LoadData(transmittalId);
                if (loadedTransmittal == null)
                {
                    //return NotFound("Transmittal not found.");
                }

                // Load Company
                var company = new Company();
                var loadedCompany = company.LoadData(int.Parse(loadedTransmittal.CompanyID));

                if (loadedCompany == null)
                {
                    //return NotFound("Company not found.");
                }

                if (ModelState.IsValid)
                {
                    _transmittalService.UpdateTransNoAndDate(int.Parse(model.Id), model.TransNo, model.Date);

                    var sheets = new Sheets();
                    foreach (var items in revEntries)
                    {
                        var sheet = sheets.LoadData(int.Parse(items.DocId));

                        foreach (var item in model.DisplayFormats)
                        {
                            if (int.Parse(items.DocId) == item.Id)
                            {
                                revEntryTransmittal.UpdateRevNoAndDate(items.Id, item.Revision, model.Date);
                            }

                            sheets.Name = item.DocumentNo;
                            sheets.Description = item.Description;
                            sheets.SheetSize = item.SheetSize;

                            //Saves all the changes on the sheet
                            if (sheets.SaveAll())
                            {

                            }
                            else
                            {

                            }
                        }
                    }

                }

                return RedirectToAction("ManageViewAllTransmittal", new { name = model.ProjectNameModal });
            }
            catch (Exception ex)
            {

            }

            return RedirectToAction("ManageViewAllTransmittal", new { name = model.ProjectNameModal });
        }

        public async Task<ActionResult> Edit(string id)
        {
            try
            {
                // Ensure id is valid and can be parsed to an integer
                if (!int.TryParse(id, out int transmittalId))
                {
                    //return BadRequest("Invalid Transmittal ID.");
                }

                // Load RevEntries
                var revEntryTransmittal = new DocRevTransmittal();
                var revEntries = await revEntryTransmittal.LoadAllByTransidAsync(transmittalId);
                if (revEntries == null)
                {
                    //return NotFound("No RevEntries found for the specified Transmittal ID.");
                }

                // Load Transmittal

                var loadedTransmittal = _transmittalService.LoadData(transmittalId);
                if (loadedTransmittal == null)
                {
                    //return NotFound("Transmittal not found.");
                }

                // Load Company
                var company = new Company();
                var loadedCompany = company.LoadData(int.Parse(loadedTransmittal.CompanyID));

                if (loadedCompany == null)
                {
                    //return NotFound("Company not found.");
                }

                var transmittalView = new TransmittalViewModel
                {
                    Id = id,
                    ProjectName = loadedTransmittal.ProjectName,
                    TransNo = loadedTransmittal.TransNo,
                    Company = loadedCompany.Name,//Must be a list of all Companies
                    Status = "IFC", //loadedTransmittal.Status,
                    Date = loadedTransmittal.Date,
                    ProjectNameModal = loadedTransmittal.ProjectName,
                    ProjectId = loadedTransmittal.ProjectID,
                    DisplayFormats = new List<TransmittalViewModel.DisplayFormatForEdit>()
                };

                var sheets = new Sheets();
                foreach (var item in revEntries)
                {
                    var sheet = sheets.LoadData(int.Parse(item.DocId));
                    if (sheet == null)
                    {
                        continue; // Skip this entry if the sheet is not found
                    }

                    var transmittalItem = new TransmittalViewModel.DisplayFormatForEdit
                    {
                        Id = sheet.Id,
                        DocumentNo = sheet.Name,
                        Description = sheet.Description,
                        Revision = item.RevNo,
                        SheetSize = sheet.SheetSize
                    };

                    transmittalView.DisplayFormats.Add(transmittalItem);
                }

                return View(transmittalView);
            }
            catch
            {

            }

            var transmittalView1 = new TransmittalViewModel();

            return View(transmittalView1);
        }

        public async Task<ActionResult> DeleteTransmittal(int id)
        {
            try
            {
                // Ensure id is valid
                if (id <= 0)
                {
                    // return BadRequest("Invalid Transmittal ID.");
                }

                // Load the transmittal to ensure it exists

                var loadedTransmittal = _transmittalService.LoadData(id);
                if (loadedTransmittal == null)
                {
                    // return NotFound("Transmittal not found.");
                }
                else
                {
                    _transmittalService.UpdateTransmittalToDeleted(id);
                }

                return RedirectToAction("ManageViewAllTransmittal", new { name = loadedTransmittal.ProjectName });
            }
            catch (Exception ex)
            {
                // Handle exceptions (log, rethrow, or return error view)
                Console.WriteLine(ex.Message);
                return View("Error");
            }
        }

        private List<TransmittalViewModel.DisplayFormatForEdit> GetDisplayFormats(ProjectModel projectData)
        {
            var formats = new List<TransmittalViewModel.DisplayFormatForEdit>();

            int count = new int[] { projectData.DocNo.Count, projectData.Revision.Count, projectData.Description.Count, projectData.SheetSize.Count }.Min();

            for (int i = 0; i < count; i++)
            {
                formats.Add(new TransmittalViewModel.DisplayFormatForEdit
                {
                    DocumentNo = projectData.DocNo[i],
                    Revision = projectData.Revision[i],
                    Description = projectData.Description[i],
                    SheetSize = projectData.SheetSize[i]
                });
            }

            return formats;
        }

        private async Task<List<TransmittalViewModel.TransmittalItem>> LoadTransmittalsAsync(string projectName)
        {
            var list = await _mongoManager.ListAllTransmittalsFromProject(_mongoManager.ConnectToDatabase(), projectName);
            return list.Select(t => new TransmittalViewModel.TransmittalItem
            {
                Company = t.Company,
                TransNo = t.TransNo.ToString(),
                Date = t.Date,
                Id = t._id.ToString(),
                ProjectName = t.Project_Name
            }).ToList();
        }

        private async Task<List<TransmittalViewModel.TransmittalItem>> LoadSQLTransmittalsAsync(string transmittalName)
        {
            try
            {
                var transmittals = await _transmittalService.ListAllByNameAsync(transmittalName);

                Company company = new Company();

                company.GetCompanyNameById(1);

                return transmittals.Select(t => new TransmittalViewModel.TransmittalItem
                {
                    Company = company.GetCompanyNameById(int.Parse(t.CompanyID)), // Assuming you want to display the CompanyID
                    TransNo = t.TransNo,
                    Date = t.Date,
                    Id = t.Id.ToString(),
                    ProjectName = t.ProjectName
                }).ToList();
            }
            catch (Exception ex)
            {
                // ex.Message;
            }

            List<TransmittalViewModel.TransmittalItem> modal = new List<TransmittalViewModel.TransmittalItem>();
            return modal;
        }

        private async Task<List<TransmittalViewModel.TransmittalItem>> LoadSQLTransmittalsAsyncWhereIsDeletedEqualsZero(string transmittalName)
        {
            try
            {
                var transmittals = await _transmittalService.ListAllByNameWhereZeroAsync(transmittalName);

                Company company = new Company();

                company.GetCompanyNameById(1);

                return transmittals.Select(t => new TransmittalViewModel.TransmittalItem
                {
                    Company = company.GetCompanyNameById(int.Parse(t.CompanyID)), // Assuming you want to display the CompanyID
                    TransNo = t.TransNo,
                    Date = t.Date,
                    Id = t.Id.ToString(),
                    ProjectName = t.ProjectName
                }).ToList();
            }
            catch (Exception ex)
            {
                // ex.Message;
            }

            List<TransmittalViewModel.TransmittalItem> modal = new List<TransmittalViewModel.TransmittalItem>();
            return modal;
        }

        public async Task<ActionResult> ManageViewAllTransmittalPDF(string TransmittalidOriginal, string projectName)
        {
            try
            {
                // Ensure id is valid and can be parsed to an integer
                if (!int.TryParse(TransmittalidOriginal, out int transmittalId))
                {
                    //return BadRequest("Invalid Transmittal ID.");
                }

                // Load RevEntries
                var revEntryTransmittal = new DocRevTransmittal();
                var revEntries = await revEntryTransmittal.LoadAllByTransidAsync(transmittalId);
                if (revEntries == null)
                {
                    //return NotFound("No RevEntries found for the specified Transmittal ID.");
                }

                //If transmittal id does not exsist
                if (transmittalId != 0)
                {


                    // Load Transmittal                    
                    var loadedTransmittal = _transmittalService.LoadData(transmittalId);
                    if (loadedTransmittal == null)
                    {
                        //return NotFound("Transmittal not found.");
                    }

                    var company = new Company();
                    // Load Company

                    var loadedCompany = company.LoadData(int.Parse(loadedTransmittal.CompanyID));

                    if (loadedCompany == null)
                    {
                        //return NotFound("Company not found.");
                    }

                    var transmittalPDFView = new TransmittalPDFViewModel
                    {
                        ProjectName = loadedTransmittal.ProjectName,
                        TransmittalNo = loadedTransmittal.TransNo,
                        Company = loadedCompany.Name,
                        //Add person
                        Transmittals = new List<TransmittalPDFViewModel.TransmittalItem>()
                    };

                    // Load Sheets
                    var sheets = new Sheets();
                    foreach (var item in revEntries)
                    {
                        var sheet = sheets.LoadData(int.Parse(item.DocId));
                        if (sheet == null)
                        {
                            continue; // Skip this entry if the sheet is not found
                        }

                        var transmittalItem = new TransmittalPDFViewModel.TransmittalItem
                        {
                            DocumentNo = sheet.Name,
                            Description = sheet.Description,
                            Rev = item.RevNo
                        };

                        transmittalPDFView.Transmittals.Add(transmittalItem);
                    }

                    return View(transmittalPDFView);
                }
            }
            catch (Exception ex)
            {
                //ex.Message;
            }


            var ErrorView = new TransmittalPDFViewModel
            {
                ProjectName = "Project Name",
                TransmittalNo = "Transmittal No",
                Company = "Company Name",
                Transmittals = new List<TransmittalPDFViewModel.TransmittalItem>()
            };

            var transmittalItem1 = new TransmittalPDFViewModel.TransmittalItem
            {
                DocumentNo = "Document Name",
                Description = "Description",
                Rev = "Revision"
            };

            ErrorView.Transmittals.Add(transmittalItem1);

            return View(ErrorView);
        }

        private async Task<TransmittalPDFViewModel> LoadTransmittalPDFDataAsync(string id, string projectName)
        {
            var data = await _mongoManager.ListAllProjectFromID(_mongoManager.ConnectToDatabase(), id);
            var projectData = data.FirstOrDefault(); // Assuming you get a single project

            if (projectData == null)
            {
                return null;
            }

            var minCount = new[] {
                projectData.DocNo?.Count ?? 0,
                projectData.Revision?.Count ?? 0,
                projectData.Description?.Count ?? 0,
                projectData.SheetSize?.Count ?? 0
            }.Min();

            var transmittalItems = new List<TransmittalPDFViewModel.TransmittalItem>();

            for (int i = 0; i < minCount; i++)
            {
                transmittalItems.Add(new TransmittalPDFViewModel.TransmittalItem
                {
                    DocumentNo = projectData.DocNo[i],
                    Rev = projectData.Revision[i]?.ToString(),
                    Description = projectData.Description[i]
                });
            }

            return new TransmittalPDFViewModel
            {
                Company = projectData.Company,
                Attention = projectData.Person,
                TransmittalNo = projectData.TransNo.ToString(),
                ProjectName = projectName,
                Transmittals = transmittalItems
            };
        }

        public ActionResult ExportPdf(TransmittalPDFViewModel model)
        {
            string projectNameToPlace = "";

            //Make sure the project name is not null
            if (!string.IsNullOrEmpty(model.ProjectName))
            {
                var ProjectNameSplit = model.ProjectName.Split('-');

                if (ProjectNameSplit.Length > 0)
                {
                    projectNameToPlace = ProjectNameSplit[0] + "-";
                }
            }

            string pdfFileName = projectNameToPlace + model.Company + "-Transmittal Slip-" + DateTime.Now.ToString("M-d-yyyy") + ".pdf";
            string appDataPath = Server.MapPath("~/App_Data");
            string pdfFilePath = Path.Combine(appDataPath, pdfFileName);

            // Ensure the directory exists
            string directoryPath = Path.GetDirectoryName(pdfFilePath.Replace(pdfFileName, ""));
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            // Call the method to generate the PDF using QuestPDF
            HtmlContentHelper.QuestPDFManageTransmittalExport(model, pdfFilePath);

            // Read the PDF file into a byte array
            byte[] pdfBytes = System.IO.File.ReadAllBytes(pdfFilePath);

            // Return the PDF file
            return File(pdfBytes, "application/pdf", pdfFileName);
        }

        public ActionResult InsertData()
        {
            return View();
        }

        public ActionResult InsertPDFTransmittals()
        {
            return View();
        }

        public ActionResult InsertExcelDrawingRegister()
        {
            return View();
        }

        [HttpGet]
        public ActionResult DebugPdfExtraction()
        {
            return View();
        }

        [HttpPost]
        public ActionResult DebugPdfExtraction(HttpPostedFileBase file)
        {
            if (file != null && file.ContentLength > 0)
            {
                var tempFilePath = Path.Combine(Server.MapPath("~/App_Data"), Path.GetFileName(file.FileName));
                file.SaveAs(tempFilePath);

                List<DrawingInfo> debugResults = ExtractDrawingInfoFromPdf(tempFilePath);

                // Clean up temp file
                if (System.IO.File.Exists(tempFilePath))
                {
                    System.IO.File.Delete(tempFilePath);
                }

                ViewBag.FileName = file.FileName;
                return View("DebugPdfResults", debugResults);
            }

            ViewBag.Message = "Please upload a valid PDF file.";
            return View();
        }

        public ActionResult UpdateDrawingRegister()
        {
            var MissingTransmittals = new MissingTransmittals();

            var LoadedMissingTransmittals = MissingTransmittals.LoadAllMissingTransmittals();

            return View(LoadedMissingTransmittals);
        }

        public ActionResult InsertMissedTransmittalsToDocRev()
        {
            string ErrorMessage = "";
            var MissingTransmittals = new MissingTransmittals();
            var LoadedMissingTransmittals = MissingTransmittals.LoadAllMissingTransmittals();

            DataAccess.Project projects = new DataAccess.Project();
            var AllProjects = projects.ListAll();



            Sheets sheets = new Sheets();

            DocRevTransmittal docRev = new DocRevTransmittal();

            Company company = new Company();

            foreach (var transmittal in LoadedMissingTransmittals)
            {
                string theDateTime = transmittal.Date;

                var _companyID = company.LoadByName(transmittal.Company);
                for (int i = 0; i < AllProjects.Count; i++)
                {
                    if (transmittal.ProjectName == AllProjects[i].Name)
                    {
                        //Create transmittal first
                        //then Doc Rev
                        var transID = _transmittalService.CreateNewTransmittal("0", "0"
                           , "0", "", theDateTime, _companyID.ToString(), "", transmittal.ProjectName, AllProjects[i].ProjectID);

                        if (transID != 0)
                        {
                            //Create DocRev
                            var sheetExist = sheets.LoadSingleSheet1(transmittal.Sheet);

                            if (sheetExist != null && sheetExist.Id != 0 && !string.IsNullOrEmpty(sheetExist.Name))
                            {
                                var docTransmittal = docRev.ListAllWhereSheetID(sheetExist.Id.ToString());

                                var docRevEntered = docRev.CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                                     transmittal.Rev, theDateTime, transID.ToString(), AllProjects[i].Id.ToString());

                                if (docRevEntered != 0)
                                {
                                    //It will remove the transmittal in the missing transmittal.
                                    MissingTransmittals.DeleteMissingTransmittal(transmittal.Id);
                                }
                                else
                                {
                                    ErrorMessage = "Revision was not inserted";
                                }
                            }
                            else
                            {
                                int SheetID = 0;

                                if (!string.IsNullOrEmpty(transmittal.Description))
                                {
                                    SheetID = sheets.InsertTheSheets(transmittal.Sheet, transmittal.Description);
                                }
                                else
                                {
                                    SheetID = sheets.InsertTheSheets(transmittal.Sheet, "");
                                }

                                //add The sheet to database            
                                DocRevTransmittal docRev1 = new DocRevTransmittal();

                                //create the transmittal
                                string projectName1 = transmittal.ProjectName;
                                string companyID = _companyID.ToString();

                                //Transmittal                                                                      
                                var docRevEntered = docRev.CreateNewDocRev(SheetID.ToString(), transmittal.Sheet,
                                                                transmittal.Rev, theDateTime, transID.ToString(), AllProjects[i].Id.ToString());

                                //CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                                //         LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                                if (docRevEntered > 0)
                                {
                                    //enterd
                                }
                                else
                                {
                                    ErrorMessage = "Revision was not inserted";
                                }
                            }
                        }
                        else
                        {
                            int SheetID = 0;

                            if (!string.IsNullOrEmpty(transmittal.Description))
                            {
                                SheetID = sheets.InsertTheSheets(transmittal.Sheet, transmittal.Description);
                            }
                            else
                            {
                                SheetID = sheets.InsertTheSheets(transmittal.Sheet, "");
                            }

                            //add The sheet to database            
                            DocRevTransmittal docRev1 = new DocRevTransmittal();

                            //create the transmittal
                            string projectName1 = transmittal.ProjectName;
                            string companyID = _companyID.ToString();

                            //Transmittal                                                                      
                            var docRevEntered = docRev.CreateNewDocRev(SheetID.ToString(), transmittal.Sheet,
                                                            transmittal.Rev, theDateTime, transID.ToString(), AllProjects[i].Id.ToString());

                            //CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                            //         LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                            if (docRevEntered > 0)
                            {
                                //enterd
                            }
                            else
                            {
                                ErrorMessage = "Revision was not inserted";
                            }
                        }
                    }
                }
            }

            return RedirectToAction("ManageTransmittals", "Transmittals");
        }

        #region Upload PDF

        [HttpPost]
        public ActionResult UploadPdf(HttpPostedFileBase file)
        {

            #region Project Name

            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            var Company = new Company();

            var loadedCompany = Company.ListAll();

            DrawingInfo drawing = new DrawingInfo();

            drawing.Project_Name = new List<string>();

            drawing.Company = new List<string>();

            foreach (var item in loadedCompany)
            {
                if (!drawing.Company.Contains(item.Name))
                {
                    drawing.Company.Add(item.Name);
                }
            }

            foreach (var item in loadedProject)
            {
                if (!drawing.Project_Name.Contains(item.Name))
                {
                    drawing.Project_Name.Add(item.Name);
                }
            }

            #endregion

            if (file != null && file.ContentLength > 0)
            {
                var tempFilePath = Path.Combine(Server.MapPath("~/App_Data"), Path.GetFileName(file.FileName));
                file.SaveAs(tempFilePath);

                List<DrawingInfo> drawings = ExtractDrawingInfoFromPdf(tempFilePath);

                return View("ExtractPdfInfo", drawings);
            }

            ViewBag.Message = "Please upload a valid PDF file.";
            return View();
        }

        [HttpPost]
        public ActionResult UploadExcel(HttpPostedFileBase file)
        {
            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            var Company = new Company();

            var loadedCompany = Company.ListAll();

            DrawingInfo drawing = new DrawingInfo();

            drawing.Project_Name = new List<string>();

            drawing.Company = new List<string>();

            foreach (var item in loadedCompany)
            {
                if (!drawing.Company.Contains(item.Name))
                {
                    drawing.Company.Add(item.Name);
                }
            }

            foreach (var item in loadedProject)
            {
                if (!drawing.Project_Name.Contains(item.Name))
                {
                    drawing.Project_Name.Add(item.Name);
                }
            }

            #endregion

            if (file != null && file.ContentLength > 0)
            {
                var tempFilePath = Path.Combine(Server.MapPath("~/App_Data"), Path.GetFileName(file.FileName));
                file.SaveAs(tempFilePath);

                List<DrawingInfo> drawings = ExtractDrawingFromExcel(tempFilePath);

                return View("ExtractPdfInfo", drawings);
            }


            ViewBag.Message = "Please upload a valid PDF file.";
            return View();
        }

        public async Task<ActionResult> GetPdfDataByProject(string projectName)
        {
            // Fetch data from MongoDB based on the selected project name
            var pdfData = await GetTheDataAndLoad(projectName);

            // Return the data as JSON
            return Json(pdfData, JsonRequestBehavior.AllowGet);
        }

        public ActionResult InsertEmptyFile()
        {
            //List from MongoDb

            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            var Company = new Company();

            var loadedCompany = Company.ListAll();

            DrawingInfo drawing = new DrawingInfo();

            drawing.Project_Name = new List<string>();

            drawing.Company = new List<string>();

            List<DrawingInfo> drawings = new List<DrawingInfo>();

            foreach (var item in loadedCompany)
            {
                if (!drawing.Company.Contains(item.Name))
                {
                    drawing.Company.Add(item.Name);
                }
            }

            foreach (var item in loadedProject)
            {
                if (!drawing.Project_Name.Contains(item.Name))
                {
                    drawing.Project_Name.Add(item.Name);
                }
            }

            var drawingInfo = new DrawingInfo
            {
                Project_Name = drawing.Project_Name,
                Company = drawing.Company,
                DocNo = "",
                Rev = "",
                Description = "",
                dates = new List<string>()
            };

            drawings.Add(drawingInfo);

            return View("ExtractPdfInfo", drawings);
        }
        public class DisplayDataItem
        {
            public string DocNo { get; set; }
            public object Revision { get; set; }
            public string Description { get; set; }
            public string Date { get; set; }
        }

        public class DisplayRevItems
        {
            public string DocNo { get; set; }
            public object Revision { get; set; }
            public string Description { get; set; }
            public string RevisionDateA { get; set; }
            public string RevisionDateB { get; set; }
            public string RevisionDateC { get; set; }
            public string RevisionDate0 { get; set; }
            public string RevisionDate1 { get; set; }
            public string RevisionDate2 { get; set; }
            public string RevisionDate3 { get; set; }
            public string RevisionDate4 { get; set; }
            public string RevisionDate5 { get; set; }
            public string RevisionDate6 { get; set; }
            public string RevisionDate7 { get; set; }
            public string RevisionDate8 { get; set; }
            public string RevisionDate9 { get; set; }
            public string RevisionDate10 { get; set; }
        }

        private List<DisplayRevItems> ProcessData2(List<string> docNos, List<object> revisions, List<string> descriptions, string date)
        {
            List<DisplayRevItems> dataItems = new List<DisplayRevItems>();
            for (int i = 0; i < docNos.Count; i++)
            {
                DisplayRevItems item = new DisplayRevItems
                {
                    DocNo = docNos[i],
                    Revision = revisions[i],
                    Description = descriptions[i],

                    RevisionDateA = CheckRevision(revisions[i], "A", date),
                    RevisionDateB = CheckRevision(revisions[i], "B", date),
                    RevisionDateC = CheckRevision(revisions[i], "C", date),
                    RevisionDate0 = CheckRevision(revisions[i], "0", date),
                    RevisionDate1 = CheckRevision(revisions[i], "1", date),
                    RevisionDate2 = CheckRevision(revisions[i], "2", date),
                    RevisionDate3 = CheckRevision(revisions[i], "3", date),
                    RevisionDate4 = CheckRevision(revisions[i], "4", date),
                    RevisionDate5 = CheckRevision(revisions[i], "5", date),
                    RevisionDate6 = CheckRevision(revisions[i], "6", date),
                    RevisionDate7 = CheckRevision(revisions[i], "7", date),
                    RevisionDate8 = CheckRevision(revisions[i], "8", date),
                    RevisionDate9 = CheckRevision(revisions[i], "9", date),
                    RevisionDate10 = CheckRevision(revisions[i], "10", date),
                };
                dataItems.Add(item);
            }

            return dataItems;
        }

        string CheckRevision(object rev, string RevDate, string date)
        {
            if (RevDate == "A" && rev.ToString() == "A")
            {
                return date;
            }

            if (RevDate == "B" && rev.ToString() == "B")
            {
                return date;
            }

            if (RevDate == "C" && rev.ToString() == "C")
            {
                return date;
            }

            if (RevDate == "0" && rev.ToString() == "0")
            {
                return date;
            }

            if (RevDate == "1" && rev.ToString() == "1")
            {
                return date;
            }

            if (RevDate == "2" && rev.ToString() == "2")
            {
                return date;
            }

            if (RevDate == "3" && rev.ToString() == "3")
            {
                return date;
            }

            if (RevDate == "4" && rev.ToString() == "4")
            {
                return date;
            }

            if (RevDate == "5" && rev.ToString() == "5")
            {
                return date;
            }

            if (RevDate == "6" && rev.ToString() == "6")
            {
                return date;
            }

            if (RevDate == "7" && rev.ToString() == "7")
            {
                return date;
            }

            if (RevDate == "8" && rev.ToString() == "8")
            {
                return date;
            }

            if (RevDate == "9" && rev.ToString() == "9")
            {
                return date;
            }

            if (RevDate == "10" && rev.ToString() == "10")
            {
                return date;
            }
            return "";
        }

        public async Task<List<DisplayRevItems>> GetTheDataAndLoad(string projectName)
        {
            var list = await _mongoManager.ListAllTransmittalsFromProject(_mongoManager.ConnectToDatabase(), projectName);

            List<DisplayDataItem> dataItems = new List<DisplayDataItem>();
            List<DisplayRevItems> revItems = new List<DisplayRevItems>();
            List<DisplayRevItems> revItems2 = new List<DisplayRevItems>();
            List<DisplayRevItems> joinedItems = new List<DisplayRevItems>();

            foreach (var project in list)
            {
                // lbCompany.Text = project.Company;

                if (project.DocNo != null)
                {
                    // Extract the required data from the ProjectModel object
                    List<string> docNos = project.DocNo;
                    List<object> revisions = project.Revision.Cast<object>().ToList();
                    List<string> descriptions = project.Description;
                    string date = project.Date;

                    //dataItems.AddRange(ProcessData(docNos, revisions, descriptions, date));

                    revItems.AddRange(ProcessData2(docNos, revisions, descriptions, date));

                    var groupedData = revItems.GroupBy(item => item.DocNo);

                    // Create a new list to hold the joined items

                    foreach (var group in groupedData)
                    {
                        // Create a new DisplayRevItems object to hold the joined data
                        var joinedItem = new DisplayRevItems
                        {
                            DocNo = group.Key, // Key is the DocNo since we're grouping by it

                            // Join the revisions, descriptions, and revision dates within the group
                            Revision = string.Join(", ", group.Select(item => item.Revision)),
                            Description = string.Join(", ", group.Select(item => item.Description)),
                            RevisionDate0 = string.Join(", ", group.Select(item => item.RevisionDate0)),
                            RevisionDate1 = string.Join(", ", group.Select(item => item.RevisionDate1)),
                            RevisionDate2 = string.Join(", ", group.Select(item => item.RevisionDate2)),
                            RevisionDate3 = string.Join(", ", group.Select(item => item.RevisionDate3)),
                            RevisionDate4 = string.Join(", ", group.Select(item => item.RevisionDate4)),
                            RevisionDate5 = string.Join(", ", group.Select(item => item.RevisionDate5)),
                            RevisionDate6 = string.Join(", ", group.Select(item => item.RevisionDate6)),
                            RevisionDate7 = string.Join(", ", group.Select(item => item.RevisionDate7)),
                            RevisionDate8 = string.Join(", ", group.Select(item => item.RevisionDate8)),
                            RevisionDate9 = string.Join(", ", group.Select(item => item.RevisionDate9)),
                            RevisionDate10 = string.Join(", ", group.Select(item => item.RevisionDate10))

                        };

                        // Add the joined item to the list
                        joinedItems.Add(joinedItem);
                    }
                }

                //lvRegisterData.DataSource = joinedItems;
                //lvRegisterData.DataBind();
            }

            return joinedItems;
        }

        [HttpPost]
        public ActionResult SaveTableData()
        {
            string jsonData;
            using (var reader = new StreamReader(Request.InputStream))
            {
                jsonData = reader.ReadToEnd();
            }

            List<DisplayRevItems> tableData = JsonConvert.DeserializeObject<List<DisplayRevItems>>(jsonData);

            List<DrawingInfo> Globaldrawings = new List<DrawingInfo>();


            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            DrawingInfo drawingProjectName = new DrawingInfo();

            drawingProjectName.Project_Name = new List<string>();

            foreach (var item in loadedProject)
            {
                drawingProjectName.Project_Name.Add(item.Name);
            }

            var Company = new Company();

            var loadedCompany = Company.ListAll();


            drawingProjectName.Company = new List<string>();

            foreach (var item in loadedCompany)
            {
                if (!drawingProjectName.Company.Contains(item.Name))
                {
                    drawingProjectName.Company.Add(item.Name);
                }
            }




            foreach (DisplayRevItems item in tableData)
            {
                List<string> dateser = new List<string>();
                string Date = "";
                List<string> revision = new List<string>();

                string docNamer = item.DocNo;
                string Decstriper = item.Description;

                if (item.Description.Contains(','))
                {
                    //string[] splitDocNo = item.DocNo.Split(',');
                    string[] splitDescription = item.Description.Split(',');

                    //docNamer = splitDocNo[0];
                    Decstriper = splitDescription[0];
                }

                item.RevisionDateA = item.RevisionDateA.Replace(",", "").Trim();
                item.RevisionDateB = item.RevisionDateB.Replace(",", "").Trim();
                item.RevisionDateC = item.RevisionDateC.Replace(",", "").Trim();
                item.RevisionDate0 = item.RevisionDate0.Replace(",", "").Trim();
                item.RevisionDate1 = item.RevisionDate1.Replace(",", "").Trim();
                item.RevisionDate2 = item.RevisionDate2.Replace(",", "").Trim();
                item.RevisionDate3 = item.RevisionDate3.Replace(",", "").Trim();
                item.RevisionDate4 = item.RevisionDate4.Replace(",", "").Trim();
                item.RevisionDate5 = item.RevisionDate5.Replace(",", "").Trim();
                item.RevisionDate6 = item.RevisionDate6.Replace(",", "").Trim();
                item.RevisionDate7 = item.RevisionDate7.Replace(",", "").Trim();
                item.RevisionDate8 = item.RevisionDate8.Replace(",", "").Trim();
                item.RevisionDate9 = item.RevisionDate9.Replace(",", "").Trim();
                item.RevisionDate10 = item.RevisionDate10.Replace(",", "").Trim();

                if (!string.IsNullOrEmpty(item.RevisionDateA))
                {
                    dateser.Add(item.RevisionDateA);
                    revision.Add("A");
                }

                if (!string.IsNullOrEmpty(item.RevisionDateB))
                {
                    dateser.Add(item.RevisionDateB);
                    revision.Add("B");
                }

                if (!string.IsNullOrEmpty(item.RevisionDateC))
                {
                    dateser.Add(item.RevisionDateC);
                    revision.Add("C");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate0))
                {
                    dateser.Add(item.RevisionDate0);
                    revision.Add("0");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate1))
                {
                    dateser.Add(item.RevisionDate1);
                    revision.Add("1");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate2))
                {
                    dateser.Add(item.RevisionDate2);
                    revision.Add("2");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate3))
                {
                    dateser.Add(item.RevisionDate3);
                    revision.Add("3");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate4))
                {
                    dateser.Add(item.RevisionDate4);
                    revision.Add("4");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate5))
                {
                    dateser.Add(item.RevisionDate5);
                    revision.Add("5");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate6))
                {
                    dateser.Add(item.RevisionDate6);
                    revision.Add("6");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate7))
                {
                    dateser.Add(item.RevisionDate7);
                    revision.Add("7");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate8))
                {
                    dateser.Add(item.RevisionDate8);
                    revision.Add("8");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate9))
                {
                    dateser.Add(item.RevisionDate9);
                    revision.Add("9");
                }

                if (!string.IsNullOrEmpty(item.RevisionDate10))
                {
                    dateser.Add(item.RevisionDate10);
                    revision.Add("10");
                }


                for (int i = 0; i < revision.Count; i++)
                {
                    List<string> dater = new List<string>();

                    dater.Add(dateser[i]);

                    DrawingInfo drawings = new DrawingInfo
                    {
                        Project_Name = drawingProjectName.Project_Name,
                        Company = drawingProjectName.Company,
                        DocNo = docNamer,
                        Description = Decstriper,
                        dates = dater,
                        Rev = revision[i]
                    };

                    Globaldrawings.Add(drawings);
                }
            }

            TempData["Globaldrawings"] = JsonConvert.SerializeObject(Globaldrawings);

            return Json(new { redirectToUrl = Url.Action("ExtractPdfInfo2", Globaldrawings) });
        }

        public ActionResult ExtractPdfInfo2(List<DrawingInfo> Globaldrawings)
        {
            if (Globaldrawings == null)
            {

                if (TempData["Globaldrawings"] != null)
                {
                    // Deserialize the data stored in TempData
                    List<DrawingInfo> Globaldrawings1 = JsonConvert.DeserializeObject<List<DrawingInfo>>(TempData["Globaldrawings"].ToString());

                    // Use Globaldrawings in the view
                    return View(Globaldrawings1);
                }
            }



            // Now you can use Globaldrawings in the view
            return View(Globaldrawings);
        }

        static List<DrawingInfo> ExtractDrawingInfoFromPdf(string pdfPath)
        {
            var drawings = new List<DrawingInfo>();
            string currentDocNo = null;


            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            DrawingInfo drawingProjectName = new DrawingInfo();

            drawingProjectName.Project_Name = new List<string>();

            foreach (var item in loadedProject)
            {
                drawingProjectName.Project_Name.Add(item.Name);
            }


            var Company = new Company();

            var loadedCompany = Company.ListAll();


            drawingProjectName.Company = new List<string>();

            foreach (var item in loadedCompany)
            {
                if (!drawingProjectName.Company.Contains(item.Name))
                {
                    drawingProjectName.Company.Add(item.Name);
                }
            }

            using (PdfDocument document = PdfDocument.Open(pdfPath))
            {
                foreach (Page page in document.GetPages())
                {
                    var text = page.Text;

                    // DEBUG: Add extracted text to a debug drawing for inspection
                    var debugDrawing = new DrawingInfo
                    {
                        Project_Name = drawingProjectName.Project_Name,
                        Company = drawingProjectName.Company,
                        DocNo = $"DEBUG_PAGE_{page.Number}",
                        Rev = "DEBUG",
                        Description = $"RAW_EXTRACTED_TEXT: {text.Replace('\n', '|').Replace('\r', '|')}",
                        dates = new List<string>()
                    };
                    drawings.Add(debugDrawing);
                    // Regex pattern to match the document number
                    string docNoPattern = @"(\d{4} - \d{4} - [A-Z]-\d{5})";
                    string pattern = @"(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4})";
                    string pattern2 = @"(?<sheetName>\d{4}[-_]\d{4}[_][A-Z]_\d{4})";
                    string pattern3 = @"^(?<sheetName>\d{4}[-_]\d{4}[_][A-Z][_]\d{4})\s+(?<revision>REV\d{2})\s+(?<description>.*)$";
                    string pattern4 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})";
                    string pattern5 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+\((?<description>.+)\)";
                    string pattern6 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4})\s+\((?<description>.+)\)";
                    string pattern7 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[A-Z]{2}[-]\d{4})\s+-\s+(?<description>.+)";
                    string pattern8 = @"(?<sheetName>\d{4}[-]\d{3}[-]\s[BS\d{4}]+)\s+-\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern9 = @"(?<sheetName>\d{4}-\d{3}-\sBS\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)-\d+";
                    string pattern10 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+?)\s+\(Rev\s+\d+\)";
                    string pattern11 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}-\d)\s+-\s+(?<description>.+)";
                    string pattern12 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+(?<description>.+)";
                    string pattern13 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4}[A-Z]?)\s+-\s+\(\s*(?<description>.+?)\s*\)\s+-\s+REV\s+\d+";
                    string pattern14 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+)";
                    string pattern15 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern16 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern17 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]+\d{3})\s+-\s+REV\s+\d+\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern18 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4}-)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern19 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern20 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4}-)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
                    string pattern21 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4})\s+-\s+\(\s*(?<description>.+?)\s*\)";

                    var match = Regex.Match(text, docNoPattern);
                    var match1 = Regex.Match(text, pattern);
                    var match2 = Regex.Match(text, pattern2);
                    var match3 = Regex.Match(text, pattern3);
                    var match4 = Regex.Match(text, pattern4);
                    var match5 = Regex.Match(text, pattern5);
                    var match6 = Regex.Match(text, pattern6);
                    var match7 = Regex.Match(text, pattern7);
                    var match8 = Regex.Match(text, pattern8);
                    var match9 = Regex.Match(text, pattern9);
                    var match10 = Regex.Match(text, pattern10);
                    var match11 = Regex.Match(text, pattern11);
                    var match12 = Regex.Match(text, pattern12);
                    var match13 = Regex.Match(text, pattern13);
                    var match14 = Regex.Match(text, pattern14);
                    var match15 = Regex.Match(text, pattern15);
                    var match16 = Regex.Match(text, pattern16);
                    var match17 = Regex.Match(text, pattern17);
                    var match18 = Regex.Match(text, pattern18);
                    var match19 = Regex.Match(text, pattern19);
                    var match20 = Regex.Match(text, pattern20);
                    var match21 = Regex.Match(text, pattern21);

                    //To filter the pattern
                    if (match.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, docNoPattern);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match1.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match2.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern2);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match3.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern3);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match4.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern4);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match5.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern5);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match6.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern6);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match7.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern7);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match8.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern8);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match9.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern9);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match10.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern10);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match11.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern11);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match12.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern12);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match13.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern13);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match14.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern14);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match15.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern15);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match16.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern16);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {

                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match17.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern17);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match18.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern18);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match19.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern19);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match20.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern20);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                    else if (match21.Success)
                    {
                        // Split the text into segments using the document number as the delimiter
                        var segments = Regex.Split(text, pattern21);

                        for (int i = 1; i < segments.Length; i++)
                        {
                            // Alternate segments will contain document numbers
                            if (i % 2 != 0)
                            {
                                string docNo = segments[i].Trim();
                                string details = segments[i + 1].Trim();

                                string revision = docNo.Substring(docNo.Length - 1);

                                docNo = docNo.Substring(0, docNo.Length - 1);

                                var drawing = new DrawingInfo
                                {
                                    Project_Name = drawingProjectName.Project_Name,
                                    Company = drawingProjectName.Company,
                                    DocNo = docNo,
                                    Rev = revision,
                                    Description = ExtractDescription(details),
                                    dates = ExtractDates(details)
                                };

                                drawings.Add(drawing);
                            }
                        }
                    }
                }

                if (drawings.Count == 0)
                {
                    var drawing = new DrawingInfo
                    {
                        Project_Name = drawingProjectName.Project_Name,
                        Company = drawingProjectName.Company,
                        DocNo = "",
                        Rev = "",
                        Description = "",
                        dates = new List<string>()
                    };

                    drawings.Add(drawing);
                }

                return drawings;
            }
        }

        static List<DrawingInfo> ExtractDrawingFromExcel(string filePath)
        {
            var drawings = new List<DrawingInfo>();

            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            DrawingInfo drawingProjectName = new DrawingInfo();

            drawingProjectName.Project_Name = new List<string>();

            foreach (var item in loadedProject)
            {
                drawingProjectName.Project_Name.Add(item.Name);
            }


            var Company = new Company();

            var loadedCompany = Company.ListAll();


            drawingProjectName.Company = new List<string>();

            foreach (var item in loadedCompany)
            {
                if (!drawingProjectName.Company.Contains(item.Name))
                {
                    drawingProjectName.Company.Add(item.Name);
                }
            }

            // Open the workbook using ClosedXML
            using (var workbook = new XLWorkbook(filePath))
            {
                // Get the first worksheet (you can also select by name)
                var worksheet = workbook.Worksheet(1);

                // Assuming the first row is the header, so data starts from the second row
                foreach (var row in worksheet.RowsUsed().Skip(1))
                {
                    //add a single date

                    // Create a new DrawingInfo instance and map the cell values to properties
                    var data = new DrawingInfo
                    {
                        // ClosedXML uses 1-based indexing for cells:
                        // Column 1: DocNo, Column 2: Description, Column 4: Rev
                        Project_Name = drawingProjectName.Project_Name,
                        Company = drawingProjectName.Company,
                        DocNo = row.Cell(1).GetValue<string>(),
                        Description = row.Cell(2).GetValue<string>(),
                        Rev = row.Cell(4).GetValue<string>(),
                        dates = new List<string>() // Initialize the Dates list if needed
                    };

                    drawings.Add(data);
                }
            }

            return drawings;
        }

        private static string ExtractDescription(string details)
        {
            // Regex pattern to find the part before the dates start
            string descriptionPattern = @"^(.*?)(\d{1,2}\s+[A-Za-z]+\d{4})";
            var match = Regex.Match(details, descriptionPattern);
            return match.Success ? match.Groups[1].Value.Trim() : details;
        }

        private static List<string> ExtractDates(string details)
        {
            // Insert spaces before each date
            string spacedDetails = Regex.Replace(details, @"(?=\d{1,2}\s+[A-Za-z]+\s*\d{4})", " ");

            // Regex pattern to match dates (e.g., "23 Feb 2023", "31 Mar 2023")
            string datePattern = @"\d{1,2}\s+[A-Za-z]+\s+\d{4}";
            var matches = Regex.Matches(spacedDetails, datePattern);
            var dates = new List<string>();

            foreach (Match match in matches)
            {
                dates.Add(match.Value);
            }

            return dates;
        }

        [HttpPost]
        public ActionResult SaveEditedPdfInfo(List<DrawingInfo> drawings, string projectName, string company)
        {
            var project = new DataAccess.Project();

            var loadedProject = project.ListAll();

            DrawingInfo drawing = new DrawingInfo();

            drawing.Project_Name = new List<string>();

            var Company = new Company();

            var loadedCompany = Company.ListAll();

            drawing.Company = new List<string>();

            var transmittals = new MissingTransmittals();

            foreach (var item in loadedCompany)
            {
                if (!drawing.Company.Contains(item.Name))
                {
                    drawing.Company.Add(item.Name);
                }
            }

            foreach (var item in loadedProject)
            {
                if (!drawing.Project_Name.Contains(item.Name))
                {
                    drawing.Project_Name.Add(item.Name);
                }
            }

            foreach (var item in drawings)
            {
                item.Project_Name = new List<string>();
                foreach (var item1 in loadedProject)
                {
                    if (!item.Project_Name.Contains(item1.Name))
                    {
                        item.Project_Name.Add(item1.Name);
                    }
                }

                item.Company = new List<string>();
                foreach (var item2 in loadedCompany)
                {
                    if (!item.Company.Contains(item2.Name))
                    {
                        item.Company.Add(item2.Name);
                    }
                }
            }


            try
            {
                foreach (var items in drawings)
                {
                    string sheets = items.DocNo;
                    string descritption = items.Description;
                    string revision = items.Rev;

                    foreach (var dates in items.dates)
                    {
                        var results = transmittals.InsertOrUpdateTransmittal(sheets, descritption, revision, projectName, company, dates);

                        if (results != 0)
                        {
                            //Find project id
                            //
                            //Insert to rev and transmittal
                        }
                    }
                }

                return View("InsertPDFTransmittals");
            }
            catch (Exception ex)
            {
                return View("ExtractPdfInfo", drawings);
            }
        }


    }
}