﻿using Autodesk.Forge.Model;
using MongoDB.Driver;
using Struxit2._0.Model;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;

namespace Struxit2._0.DataAccess
{
    public class DocRevTransmittal
    {
        /****************************************************************************************************/
        /// <summary>
        /// Summary description for Download
        /// </summary>
        public struct Item
        {
            public int Id { get; set; }
            public string DocId { get; set; }
            public string Name { get; set; }
            public string RevNo { get; set; }
            public string Date { get; set; }
            public string Transid { get; set; }
            public string ProjectID { get; set; }
        }

        public int Id { get; set; }
        public string DocId { get; set; }
        public string Name { get; set; }
        public string RevNo { get; set; }
        public string Date { get; set; }
        public string Transid { get; set; }
        public string ProjectID { get; set; }

        private readonly string connString = ConfigurationManager.ConnectionStrings["Production"].ConnectionString; //"Data Source=DevServer\\SQLEXPRESS;Initial Catalog=StruxitTwo;Integrated Security=True;TrustServerCertificate=True;"; //"Data Source=DevServer\\SQLEXPRESS;Initial Catalog=StruxitTwo;User ID=sa;Password=***************;TrustServerCertificate=True;";   //"Data Source=SKLE-AEC\\SQLEXPRESS02;Initial Catalog=StruxitTwo;Integrated Security=True;TrustServerCertificate=True;";

        /****************************************************************************************************/
        /// <summary>
        /// Initializes a new instance of the <see cref="DocRevTransmittal"/> class.
        /// </summary>
        public DocRevTransmittal()
        {
            Clear();
        }

        /****************************************************************************************************/
        /// <summary>
        /// Clears all the public variables
        /// </summary>
        public void Clear()
        {
            Id = 0;
            DocId = string.Empty;
            Name = string.Empty;
            RevNo = string.Empty;
            Date = string.Empty;
            Transid = string.Empty;
            ProjectID = string.Empty;
        }

        /****************************************************************************************************/
        /// <summary>
        /// Loads record specified by ID and populates values into the public variables
        /// </summary>
        /// <param name="ContentID">Specifies the Primary key value of the record to load</param>
        /// <returns>True on successful load, False if an error occurs</returns>
        public bool Load(int ContentID)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals] WHERE [ID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", ContentID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Id = (int)reader["id"];
                                DocId = (string)reader["DocId"];
                                Name = (string)reader["Name"];
                                RevNo = (string)reader["RevNo"];
                                Date = (string)reader["Date"];
                                Transid = (string)reader["Transid"];
                                ProjectID = (string)reader["ProjectID"];
                                return true;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Handle exception (log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
                return false;
            }
        }


        public bool LoadByNameDateAndRevision(string name, string date, string revision)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {

                try
                {
                    string query = @"
                        SELECT 
                            *
                        FROM 
                            [StruxitTwo].[dbo].[DocRevEntry]
                        WHERE 
                            [Name] = @Name AND [Date] = @Date AND [RevNo] = @RevNo;";
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand(query, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@Name", name);
                        cmd.Parameters.AddWithValue("@Date", date);
                        cmd.Parameters.AddWithValue("@RevNo", revision);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Id = (int)reader["id"];
                                DocId = (string)reader["DocId"];
                                Name = (string)reader["Name"];
                                RevNo = (string)reader["RevNo"];
                                Date = (string)reader["Date"];
                                Transid = (string)reader["Transid"];
                                ProjectID = (string)reader["ProjectID"];
                                return true;
                            }
                        }
                    }
                }
                catch (Exception ex) { Console.WriteLine(ex.Message); }
                finally
                {
                    sqlConn.Close();
                }
            }

            return false;
        }

        public bool UpdateRevNo(int id, string newRevNo, string Date)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("UPDATE [dbo].[Transmittals] SET [RevNo] = @RevNo WHERE [ID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.Parameters.AddWithValue("@RevNo", newRevNo);
                        int rowsAffected = cmd.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
                catch (Exception ex)
                {
                    // Handle exception (log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
                return false;
            }
        }

        public bool UpdateRevNoAndDate(int id, string newRevNo, string newDate)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE [dbo].[DocRevEntry]
                        SET [RevNo] = @RevNo, [Date] = @Date
                        WHERE [ID] = @ID;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.Parameters.AddWithValue("@RevNo", newRevNo);
                        cmd.Parameters.AddWithValue("@Date", newDate);
                        int rowsAffected = cmd.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
                catch (Exception ex)
                {
                    //Handle exception(log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
                return false;
            }
        }

        public async Task<List<DocRevModal>> LoadAllByTransidAsync(int transid)
        {
            List<DocRevModal> result = new List<DocRevModal>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    await sqlConn.OpenAsync();
                    string query = @"
                        SELECT 
                            *
                        FROM 
                            [StruxitTwo].[dbo].[DocRevEntry]
                        WHERE 
                            [Transid] = @Transid;";
                    using (SqlCommand cmd = new SqlCommand(query, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@Transid", transid);
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                DocRevModal entry = new DocRevModal
                                {
                                    Id = reader.GetInt32(reader.GetOrdinal("id")),
                                    DocId = reader.GetString(reader.GetOrdinal("DocId")),
                                    Name = reader.GetString(reader.GetOrdinal("Name")),
                                    RevNo = reader.GetString(reader.GetOrdinal("RevNo")),
                                    Date = reader.GetString(reader.GetOrdinal("Date")),
                                    Transid = reader.GetString(reader.GetOrdinal("Transid")),
                                    ProjectID = reader.GetString(reader.GetOrdinal("ProjectID"))
                                };
                                result.Add(entry);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Handle exception(log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            return result;
        }


        /****************************************************************************************************/
        /// <summary>
        /// Saves values contained in the public variables to a new record if Primary Key ID is 0 or updates an existing record if Primary Key ID > 0
        /// </summary>
        /// <returns>The ID of record saved or updated.</returns>
        public int Save()
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    string sql;
                    if (Id == 0)
                    {
                        sql = "INSERT INTO [dbo].[Transmittals] ([DocId], [Name], [RevNo], [Date], [TransmittalID], [ProjectID]) " +
                              "VALUES (@DocId, @Name, @RevNo, @Date, @TransmittalID, @ProjectID); " +
                              "SELECT SCOPE_IDENTITY();";
                    }
                    else
                    {
                        sql = "UPDATE [dbo].[Transmittals] SET [DocId] = @DocId, [Name] = @Name, [RevNo] = @RevNo, [Date] = @Date, " +
                              "[TransmittalID] = @TransmittalID, [ProjectID] = @ProjectID WHERE [ID] = @ID;";
                    }

                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@DocId", DocId);
                        cmd.Parameters.AddWithValue("@Name", Name);
                        cmd.Parameters.AddWithValue("@RevNo", RevNo);
                        cmd.Parameters.AddWithValue("@Date", Date);
                        cmd.Parameters.AddWithValue("@Transid", Transid);
                        cmd.Parameters.AddWithValue("@ProjectID", ProjectID);

                        if (Id != 0)
                        {
                            cmd.Parameters.AddWithValue("@ID", Id);
                            cmd.ExecuteNonQuery();
                            return Id;
                        }
                        else
                        {
                            return Convert.ToInt32(cmd.ExecuteScalar());
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Handle exception (log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
                return 0;
            }
        }

        public int UpdateDocRev(int id, int DocId, int ProjectID, int Transid, string Name, string RevNo, string Date)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    string sql = "UPDATE [dbo].[DocRevEntry] SET [DocId] = @DocId, [Name] = @Name, [RevNo] = @RevNo, [Date] = @Date, " +
                              "[Transid] = @Transid, [ProjectID] = @ProjectID WHERE [ID] = @ID;";
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@DocId", DocId);
                        cmd.Parameters.AddWithValue("@Name", Name);
                        cmd.Parameters.AddWithValue("@RevNo", RevNo);
                        cmd.Parameters.AddWithValue("@Date", Date);
                        cmd.Parameters.AddWithValue("@Transid", Transid);
                        cmd.Parameters.AddWithValue("@ProjectID", ProjectID);
                        cmd.Parameters.AddWithValue("@ID", id);
                        cmd.ExecuteNonQuery();
                        return id;
                    }
                }
                catch (Exception ex) { }
                finally
                {
                    sqlConn.Close();
                }
            }

            return 0;
        }

        public int CreateNewDocRev(string _docId, string _name, string _revNo, string _date, string _transID, string _projectID)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    string sql;

                    sql = "INSERT INTO [dbo].[DocRevEntry] ([DocId], [Name], [RevNo], [Date], [Transid], [ProjectID]) " +
                          "VALUES (@DocId, @Name, @RevNo, @Date, @Transid, @ProjectID); " +
                          "SELECT SCOPE_IDENTITY();";

                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@DocId", _docId);
                        cmd.Parameters.AddWithValue("@Name", _name);
                        cmd.Parameters.AddWithValue("@RevNo", _revNo);
                        cmd.Parameters.AddWithValue("@Date", _date);
                        cmd.Parameters.AddWithValue("@Transid", _transID);
                        cmd.Parameters.AddWithValue("@ProjectID", _projectID);


                        if (Id != 0)
                        {
                            cmd.Parameters.AddWithValue("@ID", Id);
                            cmd.ExecuteNonQuery();
                            return Id;
                        }
                        else
                        {
                            return Convert.ToInt32(cmd.ExecuteScalar());
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Handle exception (log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
                return 0;
            }
        }

        /****************************************************************************************************/
        /// <summary>
        /// Loads all records into a list
        /// </summary>
        /// <returns>A list of struct Item</returns>
        public List<Item> ListAll()
        {
            List<Item> result = new List<Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[Transmittals];", sqlConn))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Item item = new Item
                                {
                                    Id = (int)reader["id"],
                                    DocId = (string)reader["DocId"],
                                    Name = (string)reader["Name"],
                                    RevNo = (string)reader["RevNo"],
                                    Date = (string)reader["Date"],
                                    Transid = (string)reader["Transid"],
                                    ProjectID = (string)reader["ProjectID"]
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //Handle exception(log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            return result;
        }

        /****************************************************************************************************/
        /// <summary>
        /// Loads records with a specific TransmittalID into a list
        /// </summary>
        /// <param name="transmittalID">The TransmittalID to filter by</param>
        /// <returns>A list of struct Item</returns>
        public List<Item> ListAllWhereTransmittalIdIs(string Transid)
        {
            List<Item> result = new List<Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[DocRevEntry] WHERE [Transid] = @Transid;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@Transid", Transid);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Item item = new Item
                                {
                                    Id = (int)reader["id"],
                                    DocId = (string)reader["DocId"],
                                    Name = (string)reader["Name"],
                                    RevNo = (string)reader["RevNo"],
                                    Date = (string)reader["Date"],
                                    Transid = (string)reader["Transid"],
                                    ProjectID = (string)reader["ProjectID"]
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //Handle exception(log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            return result;
        }

        public List<Item> ListAllWhereTransmittalIdIsAndIsdeleted(string Transid)
        {
            List<Item> result = new List<Item>();
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[DocRevEntry] WHERE [Transid] = @Transid;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@Transid", Transid);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Item item = new Item
                                {
                                    Id = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                                    DocId = reader["DocId"] != DBNull.Value ? (string)reader["DocId"] : null,
                                    Name = reader["Name"] != DBNull.Value ? (string)reader["Name"] : null,
                                    RevNo = reader["RevNo"] != DBNull.Value ? (string)reader["RevNo"] : null,
                                    Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null,
                                    Transid = reader["Transid"] != DBNull.Value ? (string)reader["Transid"] : null,
                                    ProjectID = reader["ProjectID"] != DBNull.Value ? (string)reader["ProjectID"] : null
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //Handle exception(log it, rethrow it, or handle it appropriately)
                    Console.WriteLine(ex.Message);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            return result;
        }

        public List<Item> ListAllWhereSheetIDAndTransmittalIdIs(string docId, string transId)
        {
            List<Item> result = new List<Item>();

            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[DocRevEntry] WHERE [DocId] = @DocId AND [Transid] = @Transid;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@DocId", docId); // Replace docId with your actual DocId value
                        cmd.Parameters.AddWithValue("@Transid", transId); // Replace transId with your actual Transid value

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Item item = new Item
                                {
                                    Id = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                                    DocId = reader["DocId"] != DBNull.Value ? (string)reader["DocId"] : null,
                                    Name = reader["Name"] != DBNull.Value ? (string)reader["Name"] : null,
                                    RevNo = reader["RevNo"] != DBNull.Value ? (string)reader["RevNo"] : null,
                                    Date = reader["Date"] != DBNull.Value ? reader["Date"].ToString() : null,
                                    Transid = reader["Transid"] != DBNull.Value ? (string)reader["Transid"] : null,
                                    ProjectID = reader["ProjectID"] != DBNull.Value ? (string)reader["ProjectID"] : null
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch { }
                finally
                {
                    sqlConn.Close();
                }
            }

            return result;
        }

        public List<Item> ListAllWhereSheetID(string docId)
        {
            List<Item> result = new List<Item>();

            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM [dbo].[DocRevEntry] WHERE [DocId] = @DocId;", sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@DocId", docId); // Replace docId with your actual DocId value

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Item item = new Item
                                {
                                    Id = (int)reader["id"],
                                    DocId = (string)reader["DocId"],
                                    Name = (string)reader["Name"],
                                    RevNo = (string)reader["RevNo"],
                                    Date = (string)reader["Date"],
                                    Transid = (string)reader["Transid"],
                                    ProjectID = (string)reader["ProjectID"]
                                };
                                result.Add(item);
                            }
                        }
                    }
                }
                catch { }
                finally
                {
                    sqlConn.Close();
                }
            }

            return result;
        }

        public int RemoveTransmittal(int revisionId)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    string sql = "DELETE FROM [dbo].[DocRevEntry] WHERE [ID] = @ID;";
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@ID", revisionId);
                        cmd.ExecuteNonQuery();
                        return revisionId;
                    }
                }
                catch { }
                finally
                {
                    sqlConn.Close();
                }
            }
            return 0;
        }

        public bool LoadByRevID(int revisionId)
        {
            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                try
                {
                    string query = @"
                        SELECT 
                            *
                        FROM 
                            [StruxitTwo].[dbo].[DocRevEntry]
                        WHERE 
                            [id] = @id;";
                    sqlConn.Open();
                    using (SqlCommand cmd = new SqlCommand(query, sqlConn))
                    {
                        cmd.Parameters.AddWithValue("@id", revisionId);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Id = (int)reader["id"];
                                DocId = (string)reader["DocId"];
                                Name = (string)reader["Name"];
                                RevNo = (string)reader["RevNo"];
                                Date = (string)reader["Date"];
                                Transid = (string)reader["Transid"];
                                ProjectID = (string)reader["ProjectID"];
                                return true;
                            }
                        }
                    }
                }
                catch { }
                finally
                {
                    sqlConn.Close();
                }
            }

            return false;
        }
    }
}

