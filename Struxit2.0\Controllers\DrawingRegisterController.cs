﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using iTextSharp.tool.xml;
using iTextSharp.tool.xml.html;
//using QuestPDF.Fluent;
//using QuestPDF.Helpers;
using Struxit2._0.DataAccess;
using Struxit2._0.Helpers;
using Struxit2._0.Interfaces;
using Struxit2._0.Model;
using Struxit2._0.Services;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using static iText.IO.Image.Jpeg2000ImageData;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;

namespace Struxit2._0.Controllers
{
    public class DrawingRegisterController : Controller
    {
        private MongoDBManager _mongoDBManager = new MongoDBManager();

        private readonly PdfService _pdfService;

        private readonly ITransmittalService _transmittalService;

        public DrawingRegisterController(PdfService pdfService)
        {
            _pdfService = pdfService;
        }

        [HttpGet]
        public async Task<ActionResult> Index(string projectName, string companyName)
        {
            #region MongoDB

            //var projectData = await _mongoDBManager.ListAllTransmittalsFromProject(_mongoDBManager.ConnectToDatabase(), projectName);

            //var viewModel = new DrawingRegisterViewModel
            //{
            //    ProjectName = projectName,
            //    Company = projectData.FirstOrDefault()?.Company,
            //    Items = new List<DrawingRegisterViewModel.DisplayRevItem>()
            //};

            //var revItems = new List<DrawingRegisterViewModel.DisplayRevItem>();

            //foreach (var project in projectData)
            //{
            //    if (project.DocNo != null && project.Revision != null && project.Description != null && project.Date != null)
            //    {
            //        var docNos = project.DocNo;
            //        var revisions = project.Revision.Cast<object>().ToList();
            //        var descriptions = project.Description;
            //        var date = project.Date;

            //        if (docNos.Count == revisions.Count && revisions.Count == descriptions.Count)
            //        {
            //            if (docNos.Count > 0 && revisions.Count > 0 && descriptions.Count > 0 && !string.IsNullOrEmpty(date))
            //            {
            //                revItems.AddRange(ProcessData2(docNos, revisions, descriptions, date));
            //            }
            //        }
            //        else
            //        {
            //            int maxLength = new[] { docNos.Count, revisions.Count, descriptions.Count }.Max();
            //            while (docNos.Count < maxLength) docNos.Add("");
            //            while (revisions.Count < maxLength) revisions.Add("");
            //            while (descriptions.Count < maxLength) descriptions.Add("");

            //            revItems.AddRange(ProcessData2(docNos, revisions, descriptions, date));
            //        }
            //    }
            //}

            //var groupedData = revItems.GroupBy(item => item.DocNo);

            //foreach (var group in groupedData)
            //{
            //    var revisionsList = group.Select(item => item.Revision).ToList();
            //    var description = group.Select(item => item.Description).FirstOrDefault();

            //    string revision = GetLargeRev2(string.Join(", ", group.Select(item => item.Revision)));
            //    string RevDateA = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDateA)));
            //    string RevDateB = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDateB)));
            //    string RevDateC = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDateC)));
            //    string RevDate0 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate0)));
            //    string RevDate1 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate1)));
            //    string RevDate2 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate2)));
            //    string RevDate3 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate3)));
            //    string RevDate4 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate4)));
            //    string RevDate5 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate5)));
            //    string RevDate6 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate6)));
            //    string RevDate7 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate7)));
            //    string RevDate8 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate8)));
            //    string RevDate9 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate9)));
            //    string RevDate10 = GetLargestDate(string.Join(", ", group.Select(item => item.RevisionDate10)));

            //    var joinedItem = new DrawingRegisterViewModel.DisplayRevItem
            //    {
            //        DocNo = group.Key,
            //        Revision = revision,
            //        Description = description,
            //        RevisionDateA = RevDateA,
            //        RevisionDateB = RevDateB,
            //        RevisionDateC = RevDateC,
            //        RevisionDate0 = RevDate0,
            //        RevisionDate1 = RevDate1,
            //        RevisionDate2 = RevDate2,
            //        RevisionDate3 = RevDate3,
            //        RevisionDate4 = RevDate4,
            //        RevisionDate5 = RevDate5,
            //        RevisionDate6 = RevDate6,
            //        RevisionDate7 = RevDate7,
            //        RevisionDate8 = RevDate8,
            //        RevisionDate9 = RevDate9,
            //        RevisionDate10 = RevDate10
            //    };

            //    viewModel.Items.Add(joinedItem);
            //}

            #endregion

            // GetAllTheSheets(projectName);
            //await LoadAllMongoDataToSQLAsync(projectName);            

            #region SQL

            //[Transmittals]                      

            TransmittalRespository transmittal = new TransmittalRespository();

            #region original Method

            //Gets all the transmittals
            //var SQLprojectData = transmittal.ListAllTransmittalsFromProject(projectName);

            ////transmital
            ////Name
            ////Company
            ////person


            ////for each transmittal Get The rev doc transmittal 
            //foreach (var item in SQLprojectData)
            //{
            //    var RevDocTransmittal = new DocRevTransmittal();

            //    //For this sheet we start by getting the Revs and dates 
            //    //Then we get the name description and details then we sent to the modal

            //    //Modals

            //    //Rev
            //    //rev no 
            //    //Date

            //    //sheet
            //    //Name
            //    //rev
            //    //Description               

            //    //Add this to a list
            //    var list = RevDocTransmittal.ListAllWhereTransmittalIdIs(item.Id.ToString());

            //    //Get the Sheet Details
            //    foreach (var item2 in list)
            //    {
            //        var sheets = new Sheets();

            //        var lis2 = sheets.ListSheetWhereIDIs(item2.DocId.ToString());
            //    }
            //}

            #endregion

            List<ComprehensiveModel> comprehensiveModels = new List<ComprehensiveModel>();

            // Get all the transmittals for the given project
            //var SQLprojectData = transmittal.ListAllTransmittalsFromProjectAndCompany(projectName, companyName);

            var SQLprojectData = transmittal.ListAllTransmittalsFromProjectAndCompanyWhereIsDeleted(projectName, companyName);

            SQLprojectData = SQLprojectData.OrderBy(t => t.ProjectName).ToList();

            string _companyName = "";

            foreach (var transmittalItem in SQLprojectData)
            {
                var company = new Company();

                _companyName = company.GetCompanyNameById(int.Parse(transmittalItem.CompanyID));

                var comprehensiveModel = new ComprehensiveModel
                {
                    id = transmittalItem.Id,
                    TransmittalName = transmittalItem.ProjectName, // Assuming TransNo is the name, adjust if different
                    Company = _companyName, // Assuming CompanyID represents the company, adjust if different
                    // Assuming PersonID represents the person, adjust if different
                };

                DocRevTransmittal revDocTransmittal = new DocRevTransmittal();
                var revDocs = revDocTransmittal.ListAllWhereTransmittalIdIsAndIsdeleted(transmittalItem.Id.ToString());//ListAllWhereTransmittalIdIs(transmittalItem.Id.ToString());

                revDocs = revDocs.OrderBy(r => r.DocId).ToList();

                foreach (var revDoc in revDocs)
                {
                    Sheets sheets = new Sheets();
                    var sheetDetails = sheets.ListSheetWhereIDIs(revDoc.DocId.ToString());

                    sheetDetails = sheetDetails.OrderBy(s => s.Name).ToList();

                    foreach (var sheetDetail in sheetDetails)
                    {
                        var sheetModel = new SheetModel
                        {
                            SheetID = sheetDetail.Id,
                            Name = sheetDetail.Name,
                            Description = sheetDetail.Description,
                            SheetSize = sheetDetail.SheetSize
                        };

                        //This is where the error is
                        var revisions = revDocTransmittal.ListAllWhereSheetIDAndTransmittalIdIs(sheetDetail.Id.ToString(), transmittalItem.Id.ToString());

                        revisions = revisions.OrderBy(r => r.RevNo).ToList();

                        foreach (var revision in revisions)
                        {
                            var revisionModel = new RevisionModel
                            {
                                RevisionId = revision.Id,
                                RevNo = revision.RevNo,
                                Date = revision.Date
                            };

                            sheetModel.Revisions.Add(revisionModel);
                        }

                        comprehensiveModel.Sheets.Add(sheetModel);
                    }
                }

                comprehensiveModel.Sheets = comprehensiveModel.Sheets.OrderBy(s => s.Name).ToList();

                comprehensiveModels.Add(comprehensiveModel);
            }
            #endregion

            var allSheets = comprehensiveModels
            .SelectMany(c => c.Sheets.Select(s => new { c.TransmittalName, c.Company, Sheet = s }))
            .OrderBy(x => x.Sheet.Name)
            .ToList();

            // Create a new sorted list of ComprehensiveModels based on the sorted sheets
            var sortedComprehensiveModels = allSheets
                .GroupBy(x => new { x.TransmittalName, x.Company })
                .Select(g => new ComprehensiveModel
                {
                    TransmittalName = g.Key.TransmittalName,
                    Company = g.Key.Company,
                    Sheets = g.Select(x => x.Sheet).OrderBy(s => s.Name).ToList()
                })
                .OrderBy(cm => cm.Sheets.First().Name)
                .ToList();

            //If the sorted is not null
            if (sortedComprehensiveModels.Count > 0)
            {
                // Combine all sheets into a single list and order them alphabetically by name
                //var allSheeter = comprehensiveModels.SelectMany(cm => cm.Sheets).OrderBy(s => s.Name).ToList();

                var allSheeter = comprehensiveModels.SelectMany(cm => cm.Sheets).ToList();

                // Custom alphabetical sort
                var sortedSheets = allSheeter.OrderBy(sheet => ExtractLetters(RemoveHyphens(sheet.Name)), StringComparer.OrdinalIgnoreCase).ToList();

                var singleComprehensiveModel = new ComprehensiveModel
                {
                    id = sortedComprehensiveModels.FirstOrDefault().id,
                    TransmittalName = sortedComprehensiveModels.First().TransmittalName,
                    Company = _companyName,
                    Sheets = sortedSheets
                };

                return View(new List<ComprehensiveModel> { singleComprehensiveModel });

                //return View(sortedComprehensiveModels);
            }

            var NullComprehensiveModel = new ComprehensiveModel
            {
                id = 0,
                TransmittalName = "This company contains no data",
                Company = "This company contains no data"
            };

            List<ComprehensiveModel> NullcomprehensiveModels = new List<ComprehensiveModel>();

            NullcomprehensiveModels.Add(NullComprehensiveModel);

            return View(NullcomprehensiveModels);
        }

        private static string RemoveHyphens(string input)
        {
            return input.Replace("-", "").Replace(" ", "");
        }

        private static string ExtractLetters(string input)
        {
            return new string(input.Where(char.IsLetter).ToArray());
        }


        #region Edit Register

        // Action to display the edit form for a specific drawing register
        public ActionResult Edit(int id, int SheetID, string sheetsize, List<int> RevisionId, string name, string description, List<string> RevNo, List<string> Date, string projectName, string Company)
        {
            DocRevTransmittal revDocTransmittal = new DocRevTransmittal();
            var revDocs = revDocTransmittal.ListAllWhereTransmittalIdIsAndIsdeleted(id.ToString()); //ListAllWhereTransmittalIdIs(id.ToString());

            List<ComprehensiveModel> comprehensiveModels = new List<ComprehensiveModel>();

            var comprehensiveModel = new ComprehensiveModel
            {
                id = id,
                TransmittalName = projectName,
                Company = Company,
            };

            var sheetModel = new SheetModel
            {
                SheetID = SheetID,
                Name = name,
                Description = description,
                SheetSize = sheetsize
            };

            //foreach statement
            for (int ij = 0; ij < RevisionId.Count; ij++)
            {
                var revisionModel = new RevisionModel
                {
                    RevisionId = RevisionId[ij],
                    RevNo = RevNo[ij],
                    Date = Date[ij]
                };

                sheetModel.Revisions.Add(revisionModel);
            }

            comprehensiveModel.Sheets.Add(sheetModel);

            //foreach (var sheet in transmittalItem.Sheets)
            //{

            //@sheet.Name
            //@sheet.Description

            //@foreach(var revision in sheet.Revisions)
            //{
            //@revision.RevNo - @revision.Date
            //}
            //}


            //if (drawingRegister == null)
            //{
            //    return HttpNotFound();
            //}

            return View(comprehensiveModel);
        }



        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SaveEdit(ComprehensiveModel model)
        {
            if (ModelState.IsValid)
            {

                var company = new Company();

                string _companyName = company.GetCompanyID(model.Company);

                //TODO
                //1.Load the Project ID
                //2.Find the transmittal using the Name and the date

                TransmittalRespository transmittal = new TransmittalRespository();

                //transmittal.Save();

                //Save sheet 
                Sheets saveSheet = new Sheets();
                //saveSheet.SaveSheetWithID(model.Sheets[0].SheetID, model.Sheets[0].Name, model.Sheets[0].Description, model.Sheets[0].SheetSize);

                //save revision
                DocRevTransmittal docRev = new DocRevTransmittal();

                for (int i = 0; i < model.Sheets.Count; i++)
                {
                    for (int ia = 0; ia < model.Sheets[i].Revisions.Count; ia++)
                    {
                        if (model.Sheets[i].Revisions[ia].RevisionId != 0)
                        {
                            docRev.LoadByRevID(model.Sheets[i].Revisions[ia].RevisionId);

                            if (docRev.Id != 0)
                            {
                                if (model.Sheets[i].Revisions[ia].RevNo != null && model.Sheets[i].Revisions[ia].Date != null)
                                {
                                    docRev.UpdateDocRev(docRev.Id, model.Sheets[i].SheetID, int.Parse(docRev.ProjectID), int.Parse(docRev.Transid), model.Sheets[i].Name, model.Sheets[i].Revisions[ia].RevNo, model.Sheets[i].Revisions[ia].Date);
                                    saveSheet.SaveSheetWithID(model.Sheets[i].SheetID, model.Sheets[i].Name, model.Sheets[i].Description, model.Sheets[i].SheetSize);
                                }
                            }
                            else
                            {
                                docRev.CreateNewDocRev(model.Sheets[i].SheetID.ToString(), model.Sheets[i].Name, model.Sheets[i].Revisions[ia].RevNo, model.Sheets[i].Revisions[ia].Date, docRev.Transid, docRev.ProjectID);
                                //docRev.UpdateDocRev(docRev.Id, model.Sheets[i].SheetID, int.Parse(docRev.ProjectID), int.Parse(docRev.Transid), model.Sheets[i].Name, model.Sheets[i].Revisions[ia].RevNo, model.Sheets[i].Revisions[ia].Date);
                            }
                        }
                        else
                        {
                            docRev.CreateNewDocRev(model.Sheets[i].SheetID.ToString(), model.Sheets[i].Name, model.Sheets[i].Revisions[ia].RevNo, model.Sheets[i].Revisions[ia].Date, docRev.Transid, docRev.ProjectID);
                            // docRev.UpdateDocRev(docRev.Id, model.Sheets[i].SheetID, int.Parse(docRev.ProjectID), int.Parse(docRev.Transid), model.Sheets[i].Name, model.Sheets[i].Revisions[ia].RevNo, model.Sheets[i].Revisions[ia].Date);
                        }
                    }
                }

                //if it doesnt exsist, ADD it to the database
                //Name,Date,Revision No

                //it must compare the transmittal and if one has been changed save it 
                //if the sheet does not exist in the list remove it on the database


                //Must activate the sheet

                //update the sheet and revision for that transmittal
                //you will need the sheet id 
                //is sheet active


                //SaveDrawingRegister(model); 
                return RedirectToAction("Index", "DrawingRegister", new { projectName = model.TransmittalName, companyName = _companyName });

                //return RedirectToAction("Index"); // Redirect to the list or another page
            }

            return View("Edit", model); // If validation fails, return the same view with the model
        }


        [HttpPost]
        public ActionResult RemoveRevision(int revisionId)
        {
            if (revisionId != 0)
            {
                // Logic to remove the revision from the database
                DocRevTransmittal docRev = new DocRevTransmittal();

                docRev.RemoveTransmittal(revisionId);
            }

            return Json(new { success = true });
        }

        //// Action to process the submitted edit form
        //[HttpPost]
        //[ValidateAntiForgeryToken]
        //public ActionResult Edit(ComprehensiveModel drawingRegister)
        //{
        //    if (ModelState.IsValid)
        //    {
        //        Transmittal transmittal = new Transmittal();


        //        var existingDrawingRegister = transmittal.LoadData(drawingRegister.id);
        //        if (existingDrawingRegister != null)
        //        {
        //            existingDrawingRegister.ProjectName = drawingRegister.TransmittalName;
        //            //existingDrawingRegister.Company = drawingRegister.Company;
        //            //existingDrawingRegister.Sheets = drawingRegister.Sheets;
        //        }
        //        else
        //        {
        //            // If no existing drawing register, add the new one
        //            //drawingRegister.Id = DrawingRegisters.Any() ? DrawingRegisters.Max(dr => dr.Id) + 1 : 1;
        //            //DrawingRegisters.Add(drawingRegister);
        //        }

        //        return RedirectToAction("Edit", drawingRegister.id);
        //    }

        //    return View(drawingRegister);
        //}

        // Action to display the form for creating a new drawing register
        public ActionResult Create()
        {
            return View("Edit", new ComprehensiveModel());
        }

        // Action to process the submitted create form
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(ComprehensiveModel drawingRegister)
        {
            if (ModelState.IsValid)
            {
                //drawingRegister.Id = DrawingRegisters.Any() ? DrawingRegisters.Max(dr => dr.Id) + 1 : 1;
                //DrawingRegisters.Add(drawingRegister);

                return RedirectToAction("Index");
            }

            return View("Edit", drawingRegister);
        }

        // Optional action to delete a drawing register
        public ActionResult Delete(int id)
        {
            //var drawingRegister = DrawingRegisters.FirstOrDefault(dr => dr.Id == id);
            //if (drawingRegister != null)
            //{
            //    DrawingRegisters.Remove(drawingRegister);
            //}

            return RedirectToAction("Index");
        }


        #endregion

        [HttpGet]
        public ActionResult CreateTestData(string projectName)
        {
            if (string.IsNullOrEmpty(projectName))
            {
                ViewBag.Message = "Project name is required.";
                return View();
            }

            try
            {
                // First, ensure the database and tables exist
                EnsureDatabaseExists();

                TransmittalRespository transmittal = new TransmittalRespository();

                // Create a test transmittal for the project
                int transmittalId = transmittal.CreateNewTransmittal(
                    "TEST-001", // TransNo
                    "001", // No
                    "0", // IsDeleted (false)
                    "", // DateDeleted (empty string for null)
                    DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), // Date (proper format)
                    "1", // CompanyID (assuming company with ID 1 exists)
                    "1", // PersonID (assuming person with ID 1 exists)
                    projectName, // ProjectName
                    "1" // ProjectID
                );

                if (transmittalId > 0)
                {
                    ViewBag.Message = $"Test transmittal created successfully for project '{projectName}' with ID: {transmittalId}";
                    ViewBag.Success = true;
                }
                else
                {
                    ViewBag.Message = "Failed to create test transmittal.";
                    ViewBag.Success = false;
                }
            }
            catch (Exception ex)
            {
                ViewBag.Message = $"Error creating test data: {ex.Message}";
                ViewBag.Success = false;
            }

            ViewBag.ProjectName = projectName;
            return View();
        }

        private void EnsureDatabaseExists()
        {
            string connString = System.Configuration.ConfigurationManager.ConnectionStrings["Production"].ConnectionString;

            try
            {
                using (var connection = new System.Data.SqlClient.SqlConnection(connString))
                {
                    connection.Open();

                    // Check if Company table exists and has data
                    using (var cmd = new System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM [dbo].[Company]", connection))
                    {
                        var companyCount = (int)cmd.ExecuteScalar();
                        if (companyCount == 0)
                        {
                            // Insert default companies
                            using (var insertCmd = new System.Data.SqlClient.SqlCommand(
                                "INSERT INTO [dbo].[Company] ([Name]) VALUES ('Modena AEC'), ('Test Company')", connection))
                            {
                                insertCmd.ExecuteNonQuery();
                            }
                        }
                    }

                    // Check if Person table exists and has data
                    using (var cmd = new System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM [dbo].[Person]", connection))
                    {
                        var personCount = (int)cmd.ExecuteScalar();
                        if (personCount == 0)
                        {
                            // Insert default persons
                            using (var insertCmd = new System.Data.SqlClient.SqlCommand(
                                "INSERT INTO [dbo].[Person] ([Name]) VALUES ('John Doe'), ('Jane Smith')", connection))
                            {
                                insertCmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw - the main method will handle database issues
                System.Diagnostics.Debug.WriteLine($"Database initialization error: {ex.Message}");
            }
        }

        [HttpGet]
        public ActionResult DrawingRegister(string projectName, string hubId, string projectId)
        {
            // Redirect to SelectCompany which is the proper entry point for Drawing Register
            return RedirectToAction("SelectCompany", new { projectName });
        }

        [HttpGet]
        public async Task<ActionResult> SelectCompany(string projectName)
        {
            #region SQL

            TransmittalRespository transmittal = new TransmittalRespository();

            // Get all the transmittals for the given project
            var SQLprojectData = transmittal.ListAllTransmittalsFromProject(projectName);

            // Debug: Check if we have any data
            if (SQLprojectData == null || SQLprojectData.Count == 0)
            {
                // Log the issue and provide helpful information
                ViewBag.ErrorMessage = $"No transmittal data found for project: {projectName}. Please ensure transmittals have been created for this project.";
                ViewBag.ProjectName = projectName;
                return View("NoData");
            }

            List<CompanyModel1> CompanyList = new List<CompanyModel1>();

            foreach (var transmittalItem in SQLprojectData)
            {
                var company = new Company();

                string companyId = transmittalItem.CompanyID;
                string _companyName = company.GetCompanyNameById(int.Parse(transmittalItem.CompanyID));

                // Check if the company name already exists in the list
                if (!CompanyList.Any(c => c.Name == _companyName))
                {
                    // Add the company to the list if it doesn't exist
                    CompanyList.Add(new CompanyModel1 { id = int.Parse(companyId), Name = _companyName });
                }
            }

            var comanySelect = new CompanyViewModel
            {
                ListOfCompanies = CompanyList,
                ProjectName = projectName,
            };

            #endregion

            return View(comanySelect);
        }


        [HttpPost]
        public ActionResult ExportPdf(List<ComprehensiveModel> model)
        {
            string projectNameToPlace = "";

            //Make sure the project name is not null
            if (!string.IsNullOrEmpty(model[0].TransmittalName))
            {
                var ProjectNameSplit = model[0].TransmittalName.Split('-');
                if (ProjectNameSplit.Length > 0)
                {
                    projectNameToPlace = ProjectNameSplit[0] + "-";
                }
            }

            string pdfFileName = projectNameToPlace + model[0].Company + "-Drawing Issue Register-" + DateTime.Now.ToString("M-d-yyyy") + ".pdf";
            string appDataPath = Server.MapPath("~/App_Data");
            string pdfFilePath = Path.Combine(appDataPath, pdfFileName);

            string directoryPath = Path.GetDirectoryName(pdfFilePath.Replace(pdfFileName, ""));
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            HtmlContentHelper.QuestPDFDrawingRegisterExport(model, pdfFilePath);

            byte[] pdfBytes = System.IO.File.ReadAllBytes(pdfFilePath);

            // Return the PDF file
            return File(pdfBytes, "application/pdf", pdfFileName);

            // Render the view model to HTML using a helper method or a view
            //string htmlContent = HtmlContentHelper.ExportHtmlContentSQL(model);

            //var pdfBytes = _pdfService.GeneratePdf(htmlContent);
            // return File(pdfBytes, "application/pdf", projectNameToPlace + model[0].Company + "-Drawing Issue Register-" + DateTime.Now.ToShortDateString() + ".pdf");
        }

        #region Old Export Method
        // [HttpPost]
        //public ActionResult ExportPdf(DrawingRegisterViewModel model, string HtmlContent)
        //{
        //    var pdfBytes = _pdfService.GeneratePdf(HtmlContent);
        //    return File(pdfBytes, "application/pdf", "DrawingRegister.pdf");
        //}


        //[HttpPost]
        // public async Task<ActionResult> ExportPdf(DrawingRegisterViewModel model)
        // {
        //     // Render the HTML content to a string
        //     string htmlContent = RenderViewToString(ControllerContext, "Index", model);

        //     // Convert the HTML content to PDF
        //     var pdfBytes =  _pdfService.GeneratePdf(htmlContent);

        //     // Return the PDF file
        //     return File(pdfBytes, "application/pdf", "DrawingRegister.pdf");
        // }

        //private string RenderViewToString(ControllerContext context, string viewName, object model)
        //{
        //    context.Controller.ViewData.Model = model;

        //    using (var sw = new StringWriter())
        //    {
        //        var viewResult = ViewEngines.Engines.FindView(context, viewName, null);
        //        var viewContext = new ViewContext(context, viewResult.View, context.Controller.ViewData, context.Controller.TempData, sw);
        //        viewResult.View.Render(viewContext, sw);
        //        viewResult.ViewEngine.ReleaseView(context, viewResult.View);
        //        return sw.GetStringBuilder().ToString();
        //    }
        //}
        #endregion

        #region Old Export
        //public async Task<ActionResult> ExportPdf(DrawingRegisterViewModel model)
        //{
        //    string htmlContent = HtmlContentHelper.RenderHtmlContent(model);
        //    var pdfBytes = await _pdfService.GeneratePdfAsync(htmlContent);

        //    return File(pdfBytes, "application/pdf", "DrawingRegister.pdf");
        //}



        //[HttpPost]
        //public ActionResult ExportPdf(DrawingRegisterViewModel model)
        //{
        //    string htmlContent = HtmlContentHelper.RenderHtmlContent(model);

        //    var pdfBytes = _pdfService.GeneratePdf(htmlContent);

        //    return File(pdfBytes, "application/pdf", "DrawingRegister.pdf");
        //}
        #endregion

        private List<DrawingRegisterViewModel.DisplayRevItem> ProcessData2(List<string> docNos, List<object> revisions, List<string> descriptions, string date)
        {
            List<DrawingRegisterViewModel.DisplayRevItem> dataItems = new List<DrawingRegisterViewModel.DisplayRevItem>();
            for (int i = 0; i < docNos.Count; i++)
            {
                var item = new DrawingRegisterViewModel.DisplayRevItem
                {
                    DocNo = docNos[i],
                    Revision = revisions[i].ToString(),
                    Description = descriptions[i],
                    RevisionDateA = CheckRevision(revisions[i], "A", date),
                    RevisionDateB = CheckRevision(revisions[i], "B", date),
                    RevisionDateC = CheckRevision(revisions[i], "C", date),
                    RevisionDate0 = CheckRevision(revisions[i], "0", date),
                    RevisionDate1 = CheckRevision(revisions[i], "1", date),
                    RevisionDate2 = CheckRevision(revisions[i], "2", date),
                    RevisionDate3 = CheckRevision(revisions[i], "3", date),
                    RevisionDate4 = CheckRevision(revisions[i], "4", date),
                    RevisionDate5 = CheckRevision(revisions[i], "5", date),
                    RevisionDate6 = CheckRevision(revisions[i], "6", date),
                    RevisionDate7 = CheckRevision(revisions[i], "7", date),
                    RevisionDate8 = CheckRevision(revisions[i], "8", date),
                    RevisionDate9 = CheckRevision(revisions[i], "9", date),
                    RevisionDate10 = CheckRevision(revisions[i], "10", date),
                };
                dataItems.Add(item);
            }

            return dataItems;
        }

        private string CheckRevision(object rev, string RevDate, string date)
        {
            if (RevDate == rev.ToString()) return date;
            return "";
        }

        private string GetLargestDate(string dateTimes)
        {
            if (string.IsNullOrEmpty(dateTimes)) return "";
            string[] dateTimeStrings = dateTimes.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            DateTime largestDate = DateTime.MinValue;

            foreach (string dateTimeString in dateTimeStrings)
            {
                if (DateTime.TryParse(dateTimeString, out DateTime dateTime))
                {
                    if (dateTime > largestDate) largestDate = dateTime;
                }
            }
            return largestDate != DateTime.MinValue ? largestDate.Date.ToString("dd MMM yyyy") : "";
        }

        private string GetLargeRev2(string dateTimes)
        {
            if (string.IsNullOrEmpty(dateTimes)) return "";
            string[] values = dateTimes.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            char largestChar = 'a';
            int largestNumber = int.MinValue;

            foreach (string value in values)
            {
                char firstChar = value.Trim().FirstOrDefault();
                if (char.IsLetter(firstChar))
                {
                    if ((firstChar == 'b' || firstChar == 'B') && largestChar != 'c' && largestChar != 'C') largestChar = firstChar;
                    else if (firstChar == 'c' || firstChar == 'C') largestChar = firstChar;
                }
                else if (int.TryParse(value, out int number))
                {
                    if (number > largestNumber) largestNumber = number;
                }
            }

            if (largestNumber != int.MinValue) return largestNumber.ToString();
            return largestChar == 'a' ? "" : largestChar.ToString();
        }

        public void GetAllTheSheets(string projectName)
        {
            #region MongoLoadAllSheets

            var AllSheets = _mongoDBManager.ListAllSheetsForProject(_mongoDBManager.ConnectToDatabase(), projectName);

            //First load all the sheets
            //Make sure all the sheets dont repeat

            string connString = "Data Source=SKLE-AEC\\SQLEXPRESS02;Initial Catalog=StruxitTwo;Integrated Security=True;TrustServerCertificate=True;";


            #region OldMethod
            //using (SqlConnection sqlConn = new SqlConnection(connString))
            //{
            //    sqlConn.Open();

            //    foreach (var doc in AllSheets)
            //    {
            //        var docNoArray = doc.GetValue("DocNo").AsBsonArray;
            //        var descriptionArray = doc.GetValue("Description").AsBsonArray;
            //        //var revisionArray = doc.GetValue("Revision").AsBsonArray;


            //        if (docNoArray.Count == descriptionArray.Count)
            //        {
            //            for (int i = 0; i < docNoArray.Count; i++)
            //            {
            //                string docNo = docNoArray[i].AsString;
            //                string description = descriptionArray[i].AsString;
            //                //string revision = revisionArray[i].AsString; 

            //                // Check if the sheet already exists in the SQL database
            //                bool exists = false;
            //                using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[Sheets] WHERE [Name] = @DocNo AND [Description] = @Description", sqlConn))
            //                {
            //                    checkCmd.Parameters.AddWithValue("@DocNo", docNo);
            //                    checkCmd.Parameters.AddWithValue("@Description", description);

            //                    int count = (int)checkCmd.ExecuteScalar();
            //                    exists = count > 0;
            //                }

            //                // If the sheet does not exist, insert it into the SQL database
            //                if (!exists)
            //                {
            //                    using (SqlCommand insertCmd = new SqlCommand("INSERT INTO [dbo].[Sheets] ([Name], [Description]) VALUES (@DocNo, @Description)", sqlConn))
            //                    {
            //                        insertCmd.Parameters.AddWithValue("@DocNo", docNo);
            //                        insertCmd.Parameters.AddWithValue("@Description", description);
            //                        //insertCmd.Parameters.AddWithValue("@Revision", revision);

            //                        insertCmd.ExecuteNonQuery();
            //                    }
            //                }
            //            }
            //        }
            //        else
            //        {

            //        }



            //    }

            //    sqlConn.Close();
            //}
            #endregion


            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                sqlConn.Open();

                foreach (var doc in AllSheets)
                {
                    try
                    {
                        var docNoArray = doc.GetValue("DocNo").AsBsonArray;
                        var descriptionArray = doc.GetValue("Description").AsBsonArray;

                        if (docNoArray.Count == descriptionArray.Count)
                        {
                            for (int i = 0; i < docNoArray.Count; i++)
                            {
                                string docNo = docNoArray[i].AsString;
                                string description = descriptionArray[i].AsString;

                                // Check if the sheet already exists in the SQL database
                                bool exists = false;
                                using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[Sheets] WHERE [Name] = @DocNo AND [Description] = @Description", sqlConn))
                                {
                                    checkCmd.Parameters.AddWithValue("@DocNo", docNo);
                                    checkCmd.Parameters.AddWithValue("@Description", description);

                                    int count = (int)checkCmd.ExecuteScalar();
                                    exists = count > 0;
                                }

                                // If the sheet does not exist, insert it into the SQL database
                                if (!exists)
                                {
                                    using (SqlCommand insertCmd = new SqlCommand("INSERT INTO [dbo].[Sheets] ([Name], [Description]) VALUES (@DocNo, @Description)", sqlConn))
                                    {
                                        insertCmd.Parameters.AddWithValue("@DocNo", docNo);
                                        insertCmd.Parameters.AddWithValue("@Description", description);

                                        insertCmd.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        else
                        {
                            // Handle the case where counts do not match
                            // Log an error or take appropriate action
                            Console.WriteLine($"Document counts do not match for project: {projectName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the error and continue with the next document
                        Console.WriteLine($"Error processing document: {ex.Message}");
                        // Optionally, log detailed information for debugging
                        Console.WriteLine($"Document: {doc.ToString()}");
                    }
                }

                sqlConn.Close();
            }
            #endregion
        }

        public async Task LoadAllMongoDataToSQLAsync(string projectName)
        {
            #region Get The Transmittal And Add To Mongo

            //Get All transmittals
            var TransmittalData = await _mongoDBManager.ListAllTransmittalsFromProject(_mongoDBManager.ConnectToDatabase(), projectName);

            //var TransmittalData = await _mongoDBManager.ListAllTransmittalsFromProjectAndCompany(_mongoDBManager.ConnectToDatabase(), projectName, "Mike Buyskes Construction");


            //start with the transmittal
            string connString = "Data Source=SKLE-AEC\\SQLEXPRESS02;Initial Catalog=StruxitTwo;Integrated Security=True;TrustServerCertificate=True;";

            #region First Method

            //foreach (var trans in TransmittalData)
            //{
            //    List<object> rev = trans.Revision;
            //    List<SheetModel1> sheets = new List<SheetModel1>();
            //    int newId = 0;

            //    using (SqlConnection sqlConn = new SqlConnection(connString))
            //    {
            //        sqlConn.Open();

            //        // Check if a transmittal with the same date already exists
            //        bool exists = false;
            //        using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[Transmittals] WHERE [Date] = @Date", sqlConn))
            //        {
            //            checkCmd.Parameters.AddWithValue("@Date", trans.Date);
            //            int count = (int)checkCmd.ExecuteScalar();
            //            exists = count > 0;
            //        }

            //        if (!exists)
            //        {

            //            using (SqlCommand cmd = new SqlCommand(
            //            "INSERT INTO [dbo].[Transmittals] ([TransNo], [No], [isDeleted], [DateDeleted], [Date], [CompanyID], [PersonID], [ProjectName], [ProjectID], [Count]) " +
            //            "VALUES (@TransNo, @No, @isDeleted, @DateDeleted, @Date, @CompanyID, @PersonID, @ProjectName, @ProjectID, @Count);" +
            //            "SELECT SCOPE_IDENTITY();", sqlConn))
            //            {
            //                cmd.Parameters.AddWithValue("@TransNo", trans.TransNo);
            //                cmd.Parameters.AddWithValue("@No", trans.No);
            //                cmd.Parameters.AddWithValue("@isDeleted", trans.isDeleted);
            //                cmd.Parameters.AddWithValue("@DateDeleted", trans.dateDeleted);
            //                cmd.Parameters.AddWithValue("@Date", trans.Date);
            //                cmd.Parameters.AddWithValue("@CompanyID", 1);
            //                cmd.Parameters.AddWithValue("@PersonID", 1);
            //                cmd.Parameters.AddWithValue("@ProjectName", trans.Project_Name);
            //                cmd.Parameters.AddWithValue("@ProjectID", trans.Project_Id);
            //                cmd.Parameters.AddWithValue("@Count", trans.DocNo.Count);

            //                newId = Convert.ToInt32(cmd.ExecuteScalar());
            //            }
            //        }

            //        sqlConn.Close();
            //    }

            //    //List all Doc No
            //    for (int i = 0; i >= trans.DocNo.Count; i++)
            //    {
            //        //Search for all the doc name in SQL and id of doc 
            //        //returns a list with id, name,description
            //        //and adds to sheets

            //        using (SqlConnection sqlConn = new SqlConnection(connString))
            //        {
            //            sqlConn.Open();

            //            using (SqlCommand cmd = new SqlCommand("SELECT[id], [Name], [Description] FROM[dbo].[Sheets] WHERE[Name] = @Name; ", sqlConn))
            //            {
            //                cmd.Parameters.AddWithValue("@Name", trans.DocNo[i]);
            //                using (SqlDataReader reader = cmd.ExecuteReader())
            //                {
            //                    while (reader.Read())
            //                    {
            //                        SheetModel1 sheet = new SheetModel1
            //                        {
            //                            Id = reader.GetInt32(reader.GetOrdinal("id")),
            //                            Name = reader.GetString(reader.GetOrdinal("Name")),
            //                            Description = reader.GetString(reader.GetOrdinal("Description"))
            //                        };
            //                        sheets.Add(sheet);
            //                    }
            //                }
            //            }

            //            sqlConn.Close();
            //        }
            //        //add sheet and link to transmittal,project and company 


            //        bool inserted = false;

            //        using (SqlConnection sqlConn = new SqlConnection(connString))
            //        {
            //            sqlConn.Open();

            //            // Check if a sheet with the same date and revision number already exists
            //            bool exists = false;
            //            using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[DocRevEntry] WHERE [Date] = @Date AND [RevNo] = @RevNo", sqlConn))
            //            {
            //                checkCmd.Parameters.AddWithValue("@Date", trans.Date);
            //                checkCmd.Parameters.AddWithValue("@RevNo", trans.Revision[i].ToString());
            //                int count = (int)checkCmd.ExecuteScalar();
            //                exists = count > 0;
            //            }

            //            if (!exists)
            //            {
            //                //link the dates with the revision for one transmittal
            //                foreach (var name in sheets)
            //                {
            //                    if (name.Name == trans.DocNo[i])
            //                    {
            //                        using (SqlCommand cmd = new SqlCommand(
            //                                                       "INSERT INTO [dbo].[DocRevEntry] ([DocId], [Name], [RevNo], [Date], [Transid], [ProjectID]) " +
            //                                                       "VALUES (@DocId, @Name, @RevNo, @Date, @Transid, @ProjectID);", sqlConn))
            //                        {
            //                            cmd.Parameters.AddWithValue("@DocId", name.Id.ToString());
            //                            cmd.Parameters.AddWithValue("@Name", name.Name);
            //                            cmd.Parameters.AddWithValue("@RevNo", trans.Revision[i].ToString());
            //                            cmd.Parameters.AddWithValue("@Date", trans.Date);
            //                            cmd.Parameters.AddWithValue("@Transid", 1);
            //                            cmd.Parameters.AddWithValue("@ProjectID", 1);

            //                            cmd.ExecuteNonQuery();
            //                            inserted = true;
            //                        }
            //                    }
            //                }
            //            }

            //            sqlConn.Close();
            //        }
            //    }

            //}

            #endregion

            int GlobalCompany = 1076;

            int GlobalPerson = 0;

            int GlobalProjectID = 1095;

            using (SqlConnection sqlConn = new SqlConnection(connString))
            {
                sqlConn.Open();

                foreach (var trans in TransmittalData)
                {
                    List<SheetModel1> sheets = new List<SheetModel1>();
                    int newId = 0;

                    // Check if a transmittal with the same date already exists
                    bool transmittalExists = false;
                    using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[Transmittals] WHERE [Date] = @Date", sqlConn))
                    {
                        checkCmd.Parameters.AddWithValue("@Date", trans.Date);
                        int count = (int)checkCmd.ExecuteScalar();
                        transmittalExists = count > 0;
                    }


                    //Inserts transmittals
                    if (!transmittalExists)
                    {
                        using (SqlCommand cmd = new SqlCommand(
                            "INSERT INTO [dbo].[Transmittals] ([TransNo], [No], [isDeleted], [DateDeleted], [Date], [CompanyID], [PersonID], [ProjectName], [ProjectID], [Count]) " +
                            "VALUES (@TransNo, @No, @isDeleted, @DateDeleted, @Date, @CompanyID, @PersonID, @ProjectName, @ProjectID, @Count); " +
                            "SELECT SCOPE_IDENTITY();", sqlConn))
                        {
                            cmd.Parameters.AddWithValue("@TransNo", trans.TransNo);
                            cmd.Parameters.AddWithValue("@No", trans.No);
                            cmd.Parameters.AddWithValue("@isDeleted", trans.isDeleted);
                            cmd.Parameters.AddWithValue("@DateDeleted", "");
                            cmd.Parameters.AddWithValue("@Date", trans.Date);
                            cmd.Parameters.AddWithValue("@CompanyID", GlobalCompany);
                            cmd.Parameters.AddWithValue("@PersonID", GlobalPerson);
                            cmd.Parameters.AddWithValue("@ProjectName", trans.Project_Name);
                            cmd.Parameters.AddWithValue("@ProjectID", trans.Project_Id);
                            cmd.Parameters.AddWithValue("@Count", trans.DocNo != null ? trans.DocNo.Count : 0);

                            newId = Convert.ToInt32(cmd.ExecuteScalar());
                        }
                    }


                    if (trans.DocNo != null && trans.DocNo.Count > 0)
                    {

                        //Inserts docRevNo
                        for (int i = 0; i < trans.DocNo.Count; i++)
                        {
                            // Search for all the doc name in SQL and id of doc 
                            // returns a list with id, name,description and adds to sheets
                            using (SqlCommand cmd = new SqlCommand("SELECT [id], [Name], [Description] FROM [dbo].[Sheets] WHERE [Name] = @Name;", sqlConn))
                            {
                                cmd.Parameters.AddWithValue("@Name", trans.DocNo[i]);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        SheetModel1 sheet = new SheetModel1
                                        {
                                            Id = reader.GetInt32(reader.GetOrdinal("id")),
                                            Name = reader.GetString(reader.GetOrdinal("Name")),
                                            Description = reader.GetString(reader.GetOrdinal("Description"))
                                        };
                                        sheets.Add(sheet);
                                    }
                                }
                            }

                            // Insert the sheet and link to transmittal, project, and company
                            for (int j = 0; j < sheets.Count; j++)
                            {
                                string rev = trans.Revision != null && trans.Revision.Count > i && trans.Revision[i] != null ? trans.Revision[i].ToString() : "";

                                bool docRevEntryExists = false;
                                using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[DocRevEntry] WHERE [Date] = @Date AND [RevNo] = @RevNo AND [Name] = @Name", sqlConn))
                                {
                                    checkCmd.Parameters.AddWithValue("@Date", trans.Date);
                                    checkCmd.Parameters.AddWithValue("@RevNo", rev);
                                    checkCmd.Parameters.AddWithValue("@Name", sheets[j].Name);
                                    int count = (int)checkCmd.ExecuteScalar();
                                    docRevEntryExists = count > 0;
                                }

                                if (!docRevEntryExists)
                                {

                                    string rev1 = trans.Revision != null && trans.Revision.Count > i && trans.Revision[i] != null ? trans.Revision[i].ToString() : "";

                                    using (SqlCommand cmd = new SqlCommand(
                                        "INSERT INTO [dbo].[DocRevEntry] ([DocId], [Name], [RevNo], [Date], [Transid], [ProjectID]) " +
                                        "VALUES (@DocId, @Name, @RevNo, @Date, @Transid, @ProjectID);", sqlConn))
                                    {
                                        cmd.Parameters.AddWithValue("@DocId", sheets[j].Id);
                                        cmd.Parameters.AddWithValue("@Name", sheets[j].Name);
                                        cmd.Parameters.AddWithValue("@RevNo", rev1);
                                        cmd.Parameters.AddWithValue("@Date", trans.Date);
                                        cmd.Parameters.AddWithValue("@Transid", newId);
                                        cmd.Parameters.AddWithValue("@ProjectID", GlobalProjectID);

                                        cmd.ExecuteNonQuery();
                                    }
                                }
                            }

                        }

                        #region ForDocNo
                        //}else if(trans.DocNO != null && trans.DocNO.Count > 0)
                        //{
                        //    for (int i = 0; i < trans.DocNO.Count; i++)
                        //    {
                        //        // Search for all the doc name in SQL and id of doc 
                        //        // returns a list with id, name,description and adds to sheets
                        //        using (SqlCommand cmd = new SqlCommand("SELECT [id], [Name], [Description] FROM [dbo].[Sheets] WHERE [Name] = @Name;", sqlConn))
                        //        {
                        //            cmd.Parameters.AddWithValue("@Name", trans.DocNO[i]);
                        //            using (SqlDataReader reader = cmd.ExecuteReader())
                        //            {
                        //                while (reader.Read())
                        //                {
                        //                    SheetModel1 sheet = new SheetModel1
                        //                    {
                        //                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        //                        Name = reader.GetString(reader.GetOrdinal("Name")),
                        //                        Description = reader.GetString(reader.GetOrdinal("Description"))
                        //                    };
                        //                    sheets.Add(sheet);
                        //                }
                        //            }
                        //        }

                        //        // Insert the sheet and link to transmittal, project, and company
                        //        for (int j = 0; j < sheets.Count; j++)
                        //        {
                        //            string rev = trans.Revision != null && trans.Revision.Count > i && trans.Revision[i] != null ? trans.Revision[i].ToString() : "";

                        //            bool docRevEntryExists = false;
                        //            using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM [dbo].[DocRevEntry] WHERE [Date] = @Date AND [RevNo] = @RevNo AND [Name] = @Name", sqlConn))
                        //            {
                        //                checkCmd.Parameters.AddWithValue("@Date", trans.Date);
                        //                checkCmd.Parameters.AddWithValue("@RevNo", rev);
                        //                checkCmd.Parameters.AddWithValue("@Name", sheets[j].Name);
                        //                int count = (int)checkCmd.ExecuteScalar();
                        //                docRevEntryExists = count > 0;
                        //            }

                        //            if (!docRevEntryExists)
                        //            {

                        //                string rev1 = trans.Revision != null && trans.Revision.Count > i && trans.Revision[i] != null ? trans.Revision[i].ToString() : "";

                        //                using (SqlCommand cmd = new SqlCommand(
                        //                    "INSERT INTO [dbo].[DocRevEntry] ([DocId], [Name], [RevNo], [Date], [Transid], [ProjectID]) " +
                        //                    "VALUES (@DocId, @Name, @RevNo, @Date, @Transid, @ProjectID);", sqlConn))
                        //                {
                        //                    cmd.Parameters.AddWithValue("@DocId", sheets[j].Id);
                        //                    cmd.Parameters.AddWithValue("@Name", sheets[j].Name);
                        //                    cmd.Parameters.AddWithValue("@RevNo", rev1);
                        //                    cmd.Parameters.AddWithValue("@Date", trans.Date);
                        //                    cmd.Parameters.AddWithValue("@Transid", newId);
                        //                    cmd.Parameters.AddWithValue("@ProjectID", 1);

                        //                    cmd.ExecuteNonQuery();
                        //                }
                        //            }
                        //        }

                        //    }
                        #endregion
                    }
                }

                sqlConn.Close();
            }



            #endregion
        }


        #region Old Export and RenderHtmlContent

        //[HttpPost]
        //public ActionResult ExportPdf(DrawingRegisterViewModel model)
        //{
        //    string htmlContent = RenderHtmlContent(model);

        //    using (MemoryStream stream = new MemoryStream())
        //    {
        //        Document document = new Document(PageSize.A4.Rotate(), 50, 50, 50, 50);
        //        PdfWriter writer = PdfWriter.GetInstance(document, stream);
        //        document.Open();

        //        using (StringReader reader = new StringReader(htmlContent))
        //        {
        //            XMLWorkerHelper.GetInstance().ParseXHtml(writer, document, reader);
        //        }

        //        document.Close();
        //        return File(stream.ToArray(), "application/pdf", "DrawingRegister.pdf");
        //    }
        //}


        //public string RenderHtmlContent(DrawingRegisterViewModel model)
        //{
        //    string headerListHtml = string.Empty;
        //    string dataListHtml = string.Empty;
        //    string project = model.ProjectName;
        //    string company = model.Company;

        //    bool hasDateA = false;
        //    bool hasDateB = false;
        //    bool hasDateC = false;
        //    bool hasDate0 = false;
        //    bool hasDate1 = false;
        //    bool hasDate2 = false;
        //    bool hasDate3 = false;
        //    bool hasDate4 = false;
        //    bool hasDate5 = false;
        //    bool hasDate6 = false;
        //    bool hasDate7 = false;
        //    bool hasDate8 = false;
        //    bool hasDate9 = false;
        //    bool hasDate10 = false;

        //    headerListHtml += "<th scope=\"col\">Doc No:</th>" +
        //        "<th scope=\"col\">Rev:</th>" +
        //        "<th scope=\"col\">Description</th>";

        //    foreach (var item in model.Items)
        //    {
        //        if (item.RevisionDateA != "" && !hasDateA)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev A</th>";
        //            hasDateA = true;
        //        }

        //        if (item.RevisionDateB != "" && !hasDateB)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev B</th>";
        //            hasDateB = true;
        //        }

        //        if (item.RevisionDateC != "" && !hasDateC)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev C</th>";
        //            hasDateC = true;
        //        }

        //        if (item.RevisionDate0 != "" && !hasDate0)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 0</th>";
        //            hasDate0 = true;
        //        }

        //        if (item.RevisionDate1 != "" && !hasDate1)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 1</th>";
        //            hasDate1 = true;
        //        }

        //        if (item.RevisionDate2 != "" && !hasDate2)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 2</th>";
        //            hasDate2 = true;
        //        }

        //        if (item.RevisionDate3 != "" && !hasDate3)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 3</th>";
        //            hasDate3 = true;
        //        }

        //        if (item.RevisionDate4 != "" && !hasDate4)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 4</th>";
        //            hasDate4 = true;
        //        }

        //        if (item.RevisionDate5 != "" && !hasDate5)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 5</th>";
        //            hasDate5 = true;
        //        }

        //        if (item.RevisionDate6 != "" && !hasDate6)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 6</th>";
        //            hasDate6 = true;
        //        }

        //        if (item.RevisionDate7 != "" && !hasDate7)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 7</th>";
        //            hasDate7 = true;
        //        }

        //        if (item.RevisionDate8 != "" && !hasDate8)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 8</th>";
        //            hasDate8 = true;
        //        }

        //        if (item.RevisionDate9 != "" && !hasDate9)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 9</th>";
        //            hasDate9 = true;
        //        }

        //        if (item.RevisionDate10 != "" && !hasDate10)
        //        {
        //            headerListHtml += "<th scope=\"col\">Rev 10</th>";
        //            hasDate10 = true;
        //        }
        //    }

        //    foreach (var item in model.Items)
        //    {
        //        dataListHtml += "<tr>";
        //        dataListHtml += "<td>" + item.DocNo + "</td>";
        //        dataListHtml += "<td>" + item.Revision + "</td>";
        //        dataListHtml += "<td>" + item.Description + "</td>";

        //        if (hasDateA)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDateA + "</td>";
        //        }

        //        if (hasDateB)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDateB + "</td>";
        //        }

        //        if (hasDateC)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDateC + "</td>";
        //        }

        //        if (hasDate0)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate0 + "</td>";
        //        }

        //        if (hasDate1)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate1 + "</td>";
        //        }

        //        if (hasDate2)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate2 + "</td>";
        //        }

        //        if (hasDate3)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate3 + "</td>";
        //        }

        //        if (hasDate4)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate4 + "</td>";
        //        }
        //        if (hasDate5)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate5 + "</td>";
        //        }
        //        if (hasDate6)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate6 + "</td>";
        //        }
        //        if (hasDate7)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate7 + "</td>";
        //        }
        //        if (hasDate8)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate8 + "</td>";
        //        }
        //        if (hasDate9)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate9 + "</td>";
        //        }
        //        if (hasDate10)
        //        {
        //            dataListHtml += "<td>" + item.RevisionDate10 + "</td>";
        //        }
        //        dataListHtml += "</tr>";
        //    }

        //    string html = "<!DOCTYPE html>" +
        //                  "<html>" +
        //                  "<head>" +
        //                  "<meta charset=\"utf-8\" />" +
        //                  "<!-- Latest compiled and minified CSS -->" +
        //                  "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css\" integrity=\"sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu\" crossorigin=\"anonymous\"/>" +
        //                  "<!-- Optional theme -->\r\n" +
        //                  "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap-theme.min.css\" integrity=\"sha384-6pzBo3FDv/PJ8r2KRkGHifhEocL+1X2rVCTTkUfGk7/0pbek5mMa1upzvWbrUbOZ\" crossorigin=\"anonymous\"/>" +
        //                  "<!-- Latest compiled and minified JavaScript -->\r\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js\" integrity=\"sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd\" crossorigin=\"anonymous\"></script>" +
        //                  "<title></title>" +
        //                  "</head>" +
        //                  "<body>" +
        //                  "<div class=\"container\">" +
        //                  "<div class=\"row\">" +
        //                  "<div class=\"col-lg-12\">" +
        //                  "<div>" +
        //                  "<div>" +
        //                  "</div>" +
        //                  "</div>" +
        //                  "<div style=\"margin-top:-150px;margin-left:500px\">" +
        //                  "<hr style=\"top: 10px; height: 10px; background-color: black; border-style: solid; margin-top: 15px\" />" +
        //                  "<div style=\"background-color: lightgray\">" +
        //                  "<p class=\"text-center\">ISSUE REGISTER</p>" +
        //                  "<p class=\"text-center\">Date:" + DateTime.Now.ToString("M/d/yyyy") + ",     " + DateTime.Now.ToString("h:mm:ss") + "</p>" +
        //                  "</div>" +
        //                  "<hr style=\"top: 10px; height: 10px; background-color: black; border-style: solid;\" />" +
        //                  "</div>" +
        //                  "</div>" +
        //                  "<div class=\"col-lg-12\">" +
        //                  "<!--break Space-->" +
        //                  "<hr style=\"top: 10px; height: 10px; background-color: black; border-style: solid;\" />" +
        //                  "<div style=\"background-color: lightgray; margin-top: -16px; padding-bottom: 1px\">" +
        //                  "<p style=\"padding-top: 15px; padding-left: 15px\">Project:<b style=\"color: green\">" + project +"</b></p>\r\n" +
        //                  "</div>" +
        //                  "<br />" +
        //                  "<p>As issued to:<b style=\"color: green\">" + company + "</b></p>" +
        //                  "<!--break Space-->" +
        //                  "<table class=\"table table-bordered\">" +
        //                  "<thead>" +
        //                  "<tr>" +
        //                  headerListHtml +
        //                  "</tr>" +
        //                  "</thead>" +
        //                  "<tbody>" +
        //                  "<!--First Row-->" +
        //                  dataListHtml +
        //                  "</tbody>" +
        //                  "</table>" +
        //                  "</div>" +
        //                  "</div>" +
        //                  "</div>" +
        //                  "</body>" +
        //                  "</html>";

        //    return html;
        //}


        //[HttpPost]
        //public ActionResult ExportPdf(DrawingRegisterViewModel model)
        //{
        //    // Generate the HTML string from your model
        //    string htmlContent = RenderRazorViewToString("ExportPdfView", model);

        //    using (MemoryStream stream = new MemoryStream())
        //    {
        //        // Create a new document
        //        Document document = new Document(PageSize.A4, 50, 50, 50, 50);
        //        PdfWriter writer = PdfWriter.GetInstance(document, stream);
        //        document.Open();

        //        // Parse the HTML content
        //        using (StringReader reader = new StringReader(htmlContent))
        //        {
        //            XMLWorkerHelper.GetInstance().ParseXHtml(writer, document, reader);
        //        }

        //        // Close the document
        //        document.Close();

        //        // Return the PDF as a file
        //        return File(stream.ToArray(), "application/pdf", "DrawingRegister.pdf");
        //    }
        //}

        //// Helper method to render a view to a string
        //private string RenderRazorViewToString(string viewName, object model)
        //{
        //    ViewData.Model = model;
        //    using (var sw = new StringWriter())
        //    {
        //        var viewResult = ViewEngines.Engines.FindPartialView(ControllerContext, viewName);
        //        var viewContext = new ViewContext(ControllerContext, viewResult.View, ViewData, TempData, sw);
        //        viewResult.View.Render(viewContext, sw);
        //        return sw.GetStringBuilder().ToString();
        //    }
        //}

        #endregion

        #region Old Pdf
        //private DrawingRegisterViewModel GetDrawingRegisterViewModel()
        //{
        //    // Replace with actual data retrieval logic
        //    return new DrawingRegisterViewModel
        //    {
        //        ProjectName = "Example Project",
        //        Company = "Example Company",
        //        Items = new List<DrawingRegisterItem>
        //        {
        //            new DrawingRegisterItem { DocNo = "001", Revision = "A", Description = "First Drawing", RevisionDateA = "2022-01-01" },
        //            // Add more items as needed
        //        }
        //    };
        //}
        //}

        //[HttpPost]
        //public ActionResult ExportPdf()
        //{
        //    var model = GetDrawingRegisterViewModel();

        //    using (MemoryStream stream = new MemoryStream())
        //    {
        //        Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 10f);
        //        PdfWriter.GetInstance(pdfDoc, stream);
        //        pdfDoc.Open();

        //        // Add title
        //        pdfDoc.Add(new Paragraph($"Project: {model.ProjectName}"));
        //        pdfDoc.Add(new Paragraph($"Company: {model.Company}"));
        //        pdfDoc.Add(new Paragraph("Drawing Register"));

        //        // Create table
        //        PdfPTable table = new PdfPTable(model.Items.Count > 0 ? model.Items.First().GetType().GetProperties().Length : 1);
        //        table.WidthPercentage = 100;

        //        // Add headers
        //        table.AddCell("Doc No");
        //        table.AddCell("Rev");
        //        table.AddCell("Description");
        //        var columns = new Dictionary<string, bool>
        //        {
        //            { "Rev A", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDateA)) },
        //            { "Rev B", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDateB)) },
        //            { "Rev C", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDateC)) },
        //            { "Rev 0", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate0)) },
        //            { "Rev 1", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate1)) },
        //            { "Rev 2", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate2)) },
        //            { "Rev 3", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate3)) },
        //            { "Rev 4", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate4)) },
        //            { "Rev 5", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate5)) },
        //            { "Rev 6", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate6)) },
        //            { "Rev 7", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate7)) },
        //            { "Rev 8", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate8)) },
        //            { "Rev 9", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate9)) },
        //            { "Rev 10", model.Items.Any(i => !string.IsNullOrEmpty(i.RevisionDate10)) }
        //        };

        //        foreach (var column in columns)
        //        {
        //            if (column.Value)
        //            {
        //                table.AddCell(column.Key);
        //            }
        //        }

        //        // Add data rows
        //        foreach (var item in model.Items)
        //        {
        //            table.AddCell(item.DocNo);
        //            table.AddCell(item.Revision);
        //            table.AddCell(item.Description);
        //            if (columns["Rev A"] && !string.IsNullOrEmpty(item.RevisionDateA)) table.AddCell(item.RevisionDateA);
        //            if (columns["Rev B"] && !string.IsNullOrEmpty(item.RevisionDateB)) table.AddCell(item.RevisionDateB);
        //            if (columns["Rev C"] && !string.IsNullOrEmpty(item.RevisionDateC)) table.AddCell(item.RevisionDateC);
        //            if (columns["Rev 0"] && !string.IsNullOrEmpty(item.RevisionDate0)) table.AddCell(item.RevisionDate0);
        //            if (columns["Rev 1"] && !string.IsNullOrEmpty(item.RevisionDate1)) table.AddCell(item.RevisionDate1);
        //            if (columns["Rev 2"] && !string.IsNullOrEmpty(item.RevisionDate2)) table.AddCell(item.RevisionDate2);
        //            if (columns["Rev 3"] && !string.IsNullOrEmpty(item.RevisionDate3)) table.AddCell(item.RevisionDate3);
        //            if (columns["Rev 4"] && !string.IsNullOrEmpty(item.RevisionDate4)) table.AddCell(item.RevisionDate4);
        //            if (columns["Rev 5"] && !string.IsNullOrEmpty(item.RevisionDate5)) table.AddCell(item.RevisionDate5);
        //            if (columns["Rev 6"] && !string.IsNullOrEmpty(item.RevisionDate6)) table.AddCell(item.RevisionDate6);
        //            if (columns["Rev 7"] && !string.IsNullOrEmpty(item.RevisionDate7)) table.AddCell(item.RevisionDate7);
        //            if (columns["Rev 8"] && !string.IsNullOrEmpty(item.RevisionDate8)) table.AddCell(item.RevisionDate8);
        //            if (columns["Rev 9"] && !string.IsNullOrEmpty(item.RevisionDate9)) table.AddCell(item.RevisionDate9);
        //            if (columns["Rev 10"] && !string.IsNullOrEmpty(item.RevisionDate10)) table.AddCell(item.RevisionDate10);
        //        }

        //        pdfDoc.Add(table);
        //        pdfDoc.Close();

        //        return File(stream.ToArray(), "application/pdf", "DrawingRegister.pdf");
        //    }

        #endregion
    }
}
